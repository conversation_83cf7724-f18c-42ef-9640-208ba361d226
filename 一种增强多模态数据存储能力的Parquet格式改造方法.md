# 一种增强多模态数据存储能力的Parquet格式改造方法

## 一、所属技术领域

本发明属于数据存储技术领域，具体涉及一种改造Apache Parquet列式存储格式以增强其对多模态数据(包括图像、音频、视频、文本、时序数据等)混合存储与管理能力的技术方案。本发明尤其适用于需要同时处理和分析多种数据类型的AI应用、数据分析平台等场景。

## 二、背景技术

现有技术现状方面，Apache Parquet是一种开源的列式存储格式，最初由Twitter和Cloudera联合开发，现已成为Apache基金会的顶级项目。它通过列式存储、高效的压缩和编码方案（如Snappy, Gzip, LZO, Brotli, ZSTD；RLE, Bit-Packing, Delta Encoding等），以及谓词下推（Predicate Pushdown）等特性，极大地提升了大规模结构化和半结构化数据的存储效率和分析查询性能。这种设计存储空间占用小、查询速度快（特别是针对特定列的查询）、与大数据处理框架兼容性好。

Parquet的核心特性包括列式存储架构，支持高效的列裁剪操作；支持嵌套数据结构，可表示复杂的数据模型；内置多种压缩算法，如Snappy、Gzip、LZO等；内置多种编码方式，如Plain、Dictionary、RLE等；文件自包含元数据，便于自描述和架构演化；支持谓词下推等查询优化技术。Parquet文件结构主要包含三部分：文件头（Header）、数据块（Row Groups）和文件尾（Footer）。数据块中包含多个列块（Column Chunks），每个列块又由多个页（Pages）组成。元数据存储在文件尾部，包含文件架构、统计信息等。字节跳动等公司已对Parquet进行了深度优化，如小文件合并、列级TTL等功能。同时，Milvus等向量数据库采用专门的存储格式处理向量数据，而InfluxDB等时序数据库则针对时间序列数据优化了存储结构。

现有技术存在的问题方面，当前Parquet格式在处理多模态数据时存在以下明显不足：数据类型支持有限，Parquet主要设计用于处理结构化数据，原生仅支持基本数据类型（如整数、浮点数、字符串等）和嵌套结构，对于图像、音频、视频等非结构化数据，只能作为二进制大对象（BLOB）存储，无法利用其内部结构进行优化；存储效率低下，将多模态数据简单存储为二进制对象会导致压缩效率低下，不同模态的数据需要不同的压缩策略，但当前Parquet无法根据数据模态特性自动选择最优压缩方法；查询性能受限，缺乏针对多模态数据的特殊索引结构，导致在查询大量多模态数据时性能急剧下降，特别是对于内容检索、相似性搜索等常见多模态查询场景，现有Parquet格式无法提供有效支持；元数据管理不足，现有的元数据结构主要关注数据类型和基本统计信息，无法有效描述多模态数据的特性、关系和语义信息；跨模态分析能力弱，无法有效支持不同模态数据之间的关联分析，缺乏专门的数据结构和算法来处理跨模态查询和联合分析；可扩展性受限，难以灵活扩展以支持新的数据模态或处理方法，系统架构缺乏面向多模态数据的可扩展设计；缺乏模态感知能力，无法识别和利用不同模态数据的特性进行存储和处理优化，导致"一刀切"的处理方式，效率低下。随着多模态数据在各行业应用的日益广泛，这些限制已经成为制约大数据系统发展的瓶颈，亟需一种增强的Parquet格式来解决这些问题。

## 三、本发明创造要解决的技术问题

本发明旨在解决现有Parquet格式在处理多模态数据时面临的以下核心技术问题：如何在保持Parquet格式列式存储优势的同时，有效支持多种数据模态的高效存储；如何设计增强的元数据结构，以充分描述多模态数据的特性、关系和语义信息；如何实现模态感知的压缩和编码策略，提高多模态数据的存储效率；如何设计针对多模态数据的索引机制，支持高效的内容检索和相似性搜索；如何优化查询引擎，提高多模态数据查询和分析的性能；如何确保系统具有良好的可扩展性，支持新的数据模态和处理方法。

## 四、本发明创造的技术方案

技术原理方面，本发明提出一种增强多模态数据存储能力的Parquet格式改造方法，其核心思想是在Parquet的现有规范基础上，引入新的机制来处理和组织多模态数据。具体包括以下多个方面的改造：

首先是定义新的逻辑类型（Logical Types）与物理类型映射，专门用于表示多模态数据。定义IMAGE逻辑类型（可细分为IMAGE_JPEG, IMAGE_PNG, IMAGE_RAW等），AUDIO逻辑类型（可细分为AUDIO_MP3, AUDIO_WAV等），VIDEO逻辑类型，TEXT_EMBEDDING逻辑类型（用于存储词向量、句向量），TIMESERIES逻辑类型等。逻辑类型会映射到Parquet的物理类型（如BYTE_ARRAY），但会附带额外的元数据来解释其具体模态和编码格式。

其次是扩展Parquet的元数据结构。在ColumnMetaData中增加与模态相关的元数据字段，对于IMAGE类型包括图像尺寸（宽、高）、色彩空间、压缩格式、是否包含EXIF信息等；对于AUDIO类型包括采样率、比特率、声道数、时长、编码格式等；对于VIDEO类型包括分辨率、帧率、编码格式、时长等；对于TEXT_EMBEDDING类型包括向量维度、归一化方式等。这些元数据用于查询优化（例如，只加载符合特定尺寸的图像）和数据解析。

再次是引入多模态数据的分块与编码策略。大型媒体对象智能分块（Chunking）方面，对于大型媒体文件（如高清视频、长音频），允许将其分割成更小的、有意义的块（例如，视频的关键帧片段、音频的秒级片段）存储在Parquet的Data Page中，而不是作为一个巨大的BLOB，这有利于并行处理和选择性加载。模态特定压缩/编码集成方面，允许在Parquet的列块（Column Chunk）或数据页（Data Page）级别，除了标准的Parquet压缩（Snappy, Gzip），还支持或优先使用模态原生的压缩编码（如对图像使用JPEG内部压缩，对音频使用MP3/AAC编码），这可以避免对已压缩数据进行二次无效压缩。嵌入式轻量级特征存储方面，允许在存储原始多模态数据的同时，嵌入存储其轻量级特征（如图像的缩略图、颜色直方图；音频的MFCC特征；文本的关键词或摘要），这些特征可以直接用于快速预览或初步筛选。时序数据方面采用Delta-of-Delta和Gorilla等压缩技术。

最后是多模态数据与结构化数据的关联与同步机制。通过Parquet的行组（Row Group）结构，确保同一记录下的结构化数据与相关的多模态数据在物理存储上具有较好的局部性。利用Parquet的嵌套数据结构（Nested Structures），可以更自然地表示一个主记录关联多个不同类型的多模态对象（例如，一个产品记录包含多张图片、一段描述文本和一个介绍视频）。

系统架构方面，本系统架构旨在实现在现有大数据生态（如Hadoop, Spark）中，通过改造Parquet格式来高效存储和管理多模态数据。架构核心在于对Parquet的读写库进行扩展，并定义新的数据组织方式和元数据规范。如图1所示，本存储系统包含以下关键组件：

应用层包括数据生产者(Data Producer)和数据消费者(Data Consumer)。数据生产者是产生多模态数据的应用或服务，例如图像采集系统、语音记录服务、视频处理流水线、文本分析引擎等，它们将原始多模态数据（如图片文件、音频流、文本内容、特征向量）连同相关的结构化元数据提交给写入模块。数据消费者是需要访问和分析存储在改造后Parquet文件中的多模态数据的应用或服务，例如机器学习训练框架、数据分析平台、多模态搜索引擎等，它们通过读取模块查询和获取数据。

Parquet增强读写层包括多模态数据处理器、增强型Parquet写入器、增强型Parquet读取器和多模态数据重组器。多模态数据处理器的输入是原始多模态数据对象（如图像、音频文件）和关联的结构化数据，其功能包括元数据提取、智能分块、原生编码/轻量级特征嵌入和逻辑类型映射，输出是适合写入Parquet的二进制数据块和丰富的模态元数据。增强型Parquet写入器的输入是结构化数据、处理后的多模态数据块及其元数据，其功能包括遵循改造后的Parquet格式规范将数据写入文件，将模态特定元数据写入扩展的ColumnMetaData或FileMetaData，使用新的逻辑类型标记多模态数据列，以及管理数据页、字典页、行组的组织，确保多模态数据与关联结构化数据的局部性，输出是符合新规范的Parquet文件。增强型Parquet读取器的输入是查询请求（可能包含基于模态元数据的过滤条件），其功能包括解析改造后的Parquet文件，识别新的逻辑类型和扩展元数据，支持基于模态元数据的谓词下推，仅读取必要的数据页和列块，以及按需读取特定模态数据或其分块，输出是结构化数据、原始或分块的多模态数据及其元数据。多模态数据重组器的输入是从Parquet读取器获取的原始/分块多模态数据和元数据，其功能包括如果数据是分块存储的，则根据需要重组数据块，恢复成原始或可用的多模态对象，以及提供接口供上层应用方便地访问和使用不同模态的数据，输出是可供应用层使用的多模态数据对象。

存储层是改造后的Parquet文件(Enhanced Parquet File)，存储在分布式文件系统（如HDFS, S3）或本地文件系统。其内部结构遵循以下改造：扩展的元数据(Extended Metadata)，FileMetaData和ColumnMetaData包含新增的用于描述多模态数据属性的字段；新的逻辑类型(New Logical Types)，如IMAGE, AUDIO, VIDEO, TEXT_EMBEDDING等，用于精确定义列的数据模态；数据页(Data Pages)，存储实际数据，对于大型多模态对象，可能包含其分块或原生编码的二进制内容。

数据流交互流程分为写入流程和读取流程。写入流程是数据生产者将多模态数据和结构化数据传递给多模态数据处理器，多模态数据处理器进行元数据提取、分块（可选）、编码（可选），生成待写入的数据和元数据，增强型Parquet写入器将处理后的数据和元数据组织成改造后的Parquet格式，写入到存储层的Parquet文件中。读取流程是数据消费者发起数据读取请求，可能包含基于模态元数据的过滤条件，给增强型Parquet读取器，增强型Parquet读取器解析Parquet文件，利用扩展元数据进行谓词下推，高效读取所需数据，读取到的原始/分块多模态数据和元数据传递给多模态数据重组器，多模态数据重组器进行数据重组（如果需要），将最终的多模态对象和结构化数据返回给数据消费者。

## 五、本发明创造的效果和优点

与现有技术相比，本发明具有以下显著优点：存储效率提升方面，通过模态特定编码和避免二次压缩，可以更有效地存储已压缩的媒体数据，嵌入轻量级特征，减少对原始大文件的直接访问需求；查询与分析性能增强方面，模态元数据可用于更精细的谓词下推，减少不必要的I/O，智能分块允许选择性加载大型媒体文件的部分内容，统一存储使得跨模态数据的联合查询和分析更加直接和高效；数据管理简化与一致性保障方面，将多模态数据及其关联的结构化数据存储在同一Parquet文件中，保证了数据的原子性和一致性，避免了外部引用的管理复杂性和潜在错误；更好的生态兼容性方面，作为对Parquet的增强，可以更容易地被现有的基于Parquet的大数据处理框架（如Spark, Flink, Dask等）通过扩展读取器/写入器来支持，降低了用户的学习和迁移成本；促进多模态AI应用发展方面，为多模态机器学习、数据挖掘等应用场景提供了一个高效、便捷的底层数据存储解决方案，加速数据准备和模型训练过程。

## 七、具体实施方式

为使本发明的目的、技术方案和优点更加清楚，下面将结合附图和具体实施例，对本发明作进一步地详细描述。应当理解，此处所描述的具体实施例仅仅用以解释本发明，并不用于限定本发明。

扩展Parquet逻辑类型系统以表征多模态数据方面，定义新的模态逻辑类型(Logical Types)是本发明的关键一步，为常见的非结构化或半结构化多模态数据定义新的逻辑类型。这些逻辑类型在Parquet的物理类型（通常是BYTE_ARRAY或FIXED_LEN_BYTE_ARRAY，对于向量也可能是repeated原始类型）基础上，附加了语义信息。示例逻辑类型包括但不限于：IMAGE(format: string, width: int, height: int, channels: int, color_space: string, has_embedded_thumbnail: bool, ...)，用于表示图像数据，参数化其格式、尺寸、通道、色彩空间以及是否内嵌缩略图等；AUDIO(format: string, sample_rate: int, channels: int, bit_depth: int, duration_ms: long, ...)，用于表示音频数据，参数化其格式、采样率、声道数、位深、时长等；VIDEO_SEGMENT(format: string, codec: string, frame_rate: float, duration_ms: long, resolution: string, segment_type: enum{FULL, KEYFRAMES_ONLY, CHUNK}, ...)，用于表示视频数据或其片段，参数化其格式、编解码器、帧率、时长、分辨率、分段类型等；TEXT_RICH(encoding: string, language: string, has_markup: bool, ...)，用于表示富文本或带有特定编码/语言标记的文本；VECTOR(element_type: enum{FLOAT32, FLOAT64, INT8, ...}, dimension: int, normalization_method: string, ...)，用于表示数值型特征向量，参数化其元素类型、维度、归一化方法等；TIMESERIES(timestamp_unit: enum{MS, NS, ...}, value_schema: Schema, interpolation_method: string, ...)，用于表示时序数据。这些逻辑类型及其参数在写入时由写入器（Writer）负责编码到ColumnMetaData中，读取时由读取器（Reader）解析。

扩展Parquet元数据结构以存储模态特定信息方面，增强ColumnMetaData是实现模态感知的核心。LogicalType字段将使用上述扩展的Thrift结构来存储选定的模态逻辑类型及其具体参数。Statistics对象可以被扩展，以包含与特定模态相关的统计信息。例如，对于IMAGE类型，可以存储最小/最大宽度/高度；对于VECTOR类型，可以存储范数的min/max/avg等。这些统计信息可用于更精细的谓词下推和查询优化。

实施模态感知的数据编码与组织策略方面，原生编码保留/转换是对于本身已高效压缩的模态数据（如JPEG图像、MP3音频、H.264视频流），本方法允许在写入Parquet时直接存储其原生二进制流，并在LogicalType中标记其格式。这避免了Parquet标准压缩算法（如Snappy, Gzip）对这些数据进行低效甚至负优化的二次压缩。写入器可以根据逻辑类型智能选择是否应用Parquet的页级压缩。智能分块(Chunking)与选择性加载是对于大型多模态对象（如高清视频、长音频、高分辨率大图），本方法支持将其在逻辑上或物理上分割成更小的、有意义的块（Chunks）或片段（Segments）存储。例如，一个视频文件可以被存储为其关键帧序列，或者按时间分割成多个小的数据页。ColumnMetaData或LogicalType参数中可以包含分块信息（如每个块的大小、偏移量、索引方式）。

增强型Parquet读写器的实现方面，写入器(Enhanced Parquet Writer)的输入是结构化数据以及各种原始形态的多模态数据对象（如Java对象、文件路径、二进制流等）。处理流程包括Schema映射与逻辑类型确定，根据用户提供的Schema（其中包含了模态列的逻辑类型声明）或通过对输入数据的自省，为每个多模态列确定其在Parquet中对应的LogicalType及其参数；模态数据预处理（可选），根据逻辑类型定义和配置，执行如原生编码确认/转换、智能分块、轻量级特征提取与嵌入等操作，例如，若ImageLogicalType指定has_embedded_thumbnail=true，则写入器会生成并准备缩略图数据；数据编码与写入，结构化数据按标准Parquet方式进行编码和写入，处理后的多模态数据（可能是原生编码的二进制数据、分块数据、或特征向量数组）根据其物理类型（通常是BYTE_ARRAY或repeated原始类型）写入到相应的数据页，Parquet的页级压缩（如Snappy, Gzip）可根据策略选择性应用于这些数据页；元数据填充与写入，在相应的ColumnChunkMetaData中填充完整的LogicalType结构（包含所有参数），若有扩展的模态统计信息，也一并计算并填充，当Row Group或整个文件关闭时，包含这些增强信息的FileMetaData被写入文件尾部。输出是符合本发明改造规范的Parquet文件。

读取器(Enhanced Parquet Reader)的输入是查询请求，可能包含基于结构化属性和模态属性的谓词（例如，通过SQL或编程API指定）。处理流程包括元数据解析，读取Parquet文件的Footer，解析FileMetaData，获取Schema以及所有ColumnChunkMetaData，特别关注LogicalType字段及其参数，以及扩展的Statistics；谓词下推（包括模态谓词），对于结构化谓词，利用标准的Parquet谓词下推机制（如基于Statistics的Row Group剪枝、Page级过滤），对于模态谓词（如IMAGE.width > 1024或VECTOR.dimension = 768），读取器检查ColumnMetaData中LogicalType的参数或扩展的模态统计信息，如果一个Column Chunk的元数据表明其不可能满足模态谓词，则该Column Chunk的数据页可以被跳过读取；选择性数据加载，根据谓词下推的结果和查询投影的列，仅读取必要的数据页，如果数据是分块存储的，并且查询允许（例如，只需要视频的前几帧或图像的缩略图），则只加载相关的分块数据；数据重组与后处理，将读取的数据页反序列化，对于分块存储的多模态数据，按需重组数据块，对于原生编码的数据，将其作为二进制流提供或根据需要进行解码，最终将结构化数据和处理后的多模态数据对象组装成记录，返回给调用方。输出是满足查询条件的记录集合，每条记录包含结构化数据和相关的（可能是部分加载或重组的）多模态数据对象。

本通用实施框架的有益效果包括：普适性与灵活性，不局限于特定应用，可通过定义新的逻辑类型轻松扩展以支持未来出现的更多模态；标准化多模态元数据，通过逻辑类型参数化，为多模态数据的核心属性提供了一种标准化的描述方式，利于数据交换和互操作；深度查询优化，模态谓词下推能力是核心优势，能显著提升涉及多模态属性查询的性能，减少不必要的I/O和数据处理；存储与I/O效率，原生编码保留、智能分块和轻量级特征嵌入等策略共同优化了存储空间和数据访问效率；生态整合潜力，作为对Parquet的增强，更容易被现有大数据生态系统（如Apache Spark, Apache Flink, Dask, Trino, Presto等）通过扩展其Parquet数据源来适配和采用，从而利用这些平台的并行处理和分析能力。

本发明公开了一种增强多模态数据存储能力的Parquet格式改造方法，旨在解决现有Parquet格式在原生存储和高效处理图像、音频、视频、文本及特征向量等多种模态数据时存在的存储效率低下、查询性能瓶颈以及元数据表达不足等问题。通过对Parquet文件格式的逻辑类型系统进行扩展、元数据结构进行增强、并引入模态感知的数据组织与编码策略，本方法使得Parquet格式能够原生理解和优化多模态数据的存储。核心创新点包括定义新的多模态逻辑类型、在列元数据中嵌入丰富的模态特定属性、支持智能分块和原生编码保留，以及可选的轻量级特征嵌入。该改造方法在保持Parquet列式存储和高效压缩（针对结构化部分）核心优势的同时，显著提升了其在统一存储、高效查询和便捷分析混合多模态数据方面的能力，为大数据生态系统中的多模态数据管理和AI应用提供了更优化的底层存储解决方案，具有显著的技术进步和应用价值。