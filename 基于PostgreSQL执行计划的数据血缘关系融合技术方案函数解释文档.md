# 函数解释文档

## PostgreSQL原有函数

### 查询处理主流程
- **exec_simple_query** - 处理用户SQL命令的主入口函数
- **pg_parse_query** - 执行词法和语法分析
- **raw_parser** - 底层解析器，将SQL转换为分析树
- **parse_analyze** - 语义分析，生成查询树
- **pg_rewrite_query** - 处理规则系统和视图展开
- **planner** - 优化器入口，生成执行计划

### 查询执行
- **ExecutorStart** - 执行器启动，初始化执行状态
- **ExecutorRun** - 执行器核心驱动函数
- **ExecutorFinish** - 执行器结束函数
- **ExecutorEnd** - 执行器清理函数

### 节点处理函数
- **ExecSeqScan** - 顺序扫描
- **ExecIndexScan** - 索引扫描
- **ExecNestLoop** - 嵌套循环连接
- **ExecHashJoin** - 哈希连接
- **ExecMergeJoin** - 合并连接
- **ExecAgg** - 聚合操作
- **ExecSort** - 排序操作
- **ExecModifyTable** - 数据修改操作

## 自主设计函数

### 血缘采集回调函数
- **on_query_start_initialize_lineage_callback** - 初始化血缘采集，分配Query ID
- **on_raw_parse_extract_basic_lineage_callback** - 提取基础血缘信息（表名、列名等）
- **on_analyze_identify_relations_callback** - 识别表级和列级依赖关系
- **on_rewrite_track_changes_callback** - 追踪重写阶段的血缘变更
- **on_executor_start_initialize_tracker_callback** - 初始化执行阶段血缘跟踪器
- **on_executor_end_finalize_lineage_callback** - 合并静态和动态血缘信息

### 核心组件
- **PlanLineageExtractor** - 从执行计划树提取列级血缘关系
- **LineageCollector** - 血缘信息收集器，存储查询生命周期中的血缘数据
- **ExecutionLineageTracker** - 执行阶段血缘跟踪器，收集运行时动态信息
- **ViewMappingTracker** - 视图映射跟踪器，处理视图展开的血缘关系

### 血缘查询函数
- **pg_lineage.get_table_lineage** - 查询表的上游/下游血缘关系
- **pg_lineage.get_column_lineage** - 查询列级血缘关系
- **pg_lineage.get_query_lineage** - 查询单个SQL的血缘关系
- **pg_lineage.analyze_impact** - 分析数据变更的影响范围
- **pg_lineage.analyze_dependencies** - 追踪数据的上游依赖
- **pg_lineage.find_lineage_path** - 查找两个对象间的血缘路径
- **pg_lineage.generate_lineage_stats** - 生成血缘统计信息

## 其他项目

### 数学模型系数
- **complexity(O_i)** - 操作复杂度系数，用于量化不同操作的血缘复杂程度
  - 简单列引用：1
  - 条件过滤：2  
  - 内连接：3
  - 外连接：4
  - 聚合函数：5
  - 嵌套子查询：n（嵌套层数）

### 需要明确的项目
- **QueryRewrite** - 文档中提到但未明确是PostgreSQL原有还是自设计的内部函数
