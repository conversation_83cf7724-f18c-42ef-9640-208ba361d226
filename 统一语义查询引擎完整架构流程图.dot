digraph UnifiedSemanticQueryEngine {
    rankdir=TB;
    node [shape=box, style=filled, fontname="SimHei", fontsize=10];
    edge [fontname="SimHei", fontsize=9];

    // 用户查询输入层
    subgraph cluster_input {
        label="多模态查询输入";
        style=filled;
        color=lightblue;

        structured_query [label="结构化查询\n扩展SQL语法\n保持传统SQL兼容\n(IMAGE_SIMILAR_TO,\nTEXT_CONTAINS,\nAUDIO_MATCHES)", fillcolor=lightgreen];
        api_interface [label="程序化API接口\n现代化API设计\n多模态内容原生支持\n层次化设计", fillcolor=lightgreen];
        natural_language [label="自然语言查询\n意图识别模型\n实体提取与消歧\n上下文感知能力\n交互式澄清机制", fillcolor=lightgreen];
    }

    // 查询解析与语义理解模块
    subgraph cluster_parsing {
        label="查询解析与语义理解模块";
        style=filled;
        color=lightyellow;

        query_parsing_module [label="查询解析与语义理解模块\n基于USR模型\n统一处理多种输入方式\n执行计划感知的预分析", fillcolor=yellow];

        // 查询内容语义编码转换
        subgraph cluster_semantic_encoding {
            label="查询内容语义编码转换";
            style=filled;
            color=wheat;

            mixed_query_processing [label="混合查询处理\n多模态查询向量融合\n执行计划感知机制\n权重预估算", fillcolor=white];
            semantic_vector_generation [label="语义向量生成\n基于SEncoder编码\n统一语义空间映射", fillcolor=white];
        }
    }

    // USR模型核心
    subgraph cluster_usr {
        label="USR模型 (统一语义表示模型)";
        style=filled;
        color=lightcoral;

        subgraph cluster_sencoder {
            label="SEncoder编码器 (语义编码器)";
            style=filled;
            color=mistyrose;

            text_encoder [label="文本SEncoder\nE_text(x_text)\nTransformer架构\n分词清洗→语义编码→L2归一化", fillcolor=white];
            image_encoder [label="图像SEncoder\nE_image(x_image)\nResNet/Vision Transformer\n预处理→特征提取→跨模态对齐", fillcolor=white];
            audio_encoder [label="音频SEncoder\nE_audio(x_audio)\nWav2Vec架构\n时频分析→深度学习", fillcolor=white];
            video_encoder [label="视频SEncoder\nE_video(x_video)\n时空卷积网络\n帧级特征→时序建模", fillcolor=white];
        }

        unified_semantic_vector [label="统一语义向量\n768维L2归一化\n向量模长=1\n维度值域[-1,1]\n余弦相似度计算", fillcolor=lightpink];

        subgraph cluster_mfusion {
            label="MFusion融合器 (多模态融合器)";
            style=filled;
            color=pink;

            three_factor_calculation [label="三因子动态权重计算\nα_i: 语义相关性系数\nγ_i: 执行计划感知系数\nδ_i: 资源效率系数", fillcolor=white];
            weight_normalization [label="权重归一化\nβ_i = (α_i·γ_i·δ_i) / Σ(α_j·γ_j·δ_j)\n确保Σβ_i=1且β_i≥0", fillcolor=white];
            fusion_formula [label="融合公式\nF(E_m1(x1),...,E_mn(xn))\n= Σ(β_i·E_mi(xi))", fillcolor=white];
        }

        cross_modal_alignment [label="跨模态语义对齐机制\n语义相似度矩阵构建\nsoftmax对齐权重计算\n跨模态信息交互\nv_i' = v_i + Σ(align_ij·v_j)", fillcolor=lavender];
    }

    // 查询优化与规划模块
    subgraph cluster_optimization {
        label="查询优化与规划模块 (语义感知查询优化框架)";
        style=filled;
        color=lightsteelblue;

        semantic_aware_analysis [label="语义感知查询分析\n查询语义特征识别\n模态类型分析\n语义复杂度评估", fillcolor=white];
        intelligent_index_selection [label="智能索引选择\n语义向量索引策略\nLSH/HNSW技术\n多模态索引优化", fillcolor=white];
        execution_plan_generation [label="语义感知执行计划生成\n基于MFusion权重结果\n动态优先级调整\n资源分配策略", fillcolor=white];
        postgresql_integration [label="PostgreSQL集成\nEXPLAIN ANALYZE输出\nJSON格式执行计划解析\n特征标准化接口", fillcolor=orange];
    }

    // 多模态查询执行模块
    subgraph cluster_execution {
        label="多模态查询执行模块";
        style=filled;
        color=lightseagreen;

        subgraph cluster_execution_paths {
            label="并行执行路径";
            style=filled;
            color=white;

            text_retrieval_path [label="文本检索路径\n文本语义检索\n索引扫描优化", fillcolor=lightcyan];
            image_retrieval_path [label="图像检索路径\n图像语义匹配\n高维向量处理", fillcolor=lightcyan];
            cross_modal_path [label="跨模态关联路径\n跨模态语义对齐\n关联强度计算", fillcolor=lightcyan];
        }

        dynamic_weight_adjustment [label="动态权重调整机制\n执行监控与反馈收集\n触发条件判断\n权重重新计算", fillcolor=lightgreen];
        semantic_similarity_search [label="语义相似度搜索\n余弦相似度计算\n统一语义空间检索\n跨模态相似性搜索", fillcolor=lightgreen];
    }

    // 数据存储层
    subgraph cluster_storage {
        label="多模态数据存储";
        style=filled;
        color=lightgray;

        data_preprocessing [label="多模态数据预处理\n语义编码流程\nSEncoder一致性保障\n批量处理优化", fillcolor=white];
        semantic_vector_storage [label="语义向量存储\n扩展表结构设计\n768维向量存储\n一对一映射关系", fillcolor=white];
        vector_indexing [label="向量索引系统\nLSH/HNSW算法\n高维向量优化\n近似最近邻搜索", fillcolor=white];
        cross_modal_cache [label="跨模态对齐缓存\n存储1000次查询结果\n加速相似查询处理", fillcolor=white];
    }

    // 查询结果融合模块
    subgraph cluster_fusion {
        label="查询结果融合模块";
        style=filled;
        color=plum;

        weighted_fusion_scoring [label="加权融合评分\nScore_final = Σ(β_i × Score_semantic_i)\n多路径结果整合", fillcolor=white];
        semantic_aware_sorting [label="语义感知结果排序\n基于USR模型\n统一语义空间相似度计算", fillcolor=white];
        execution_feedback_fusion [label="基于执行反馈的融合\n动态权重应用\n结果质量优化", fillcolor=white];
    }

    // 自优化机制
    subgraph cluster_self_optimization {
        label="基于执行反馈的自优化机制";
        style=filled;
        color=lightsteelblue;

        feedback_collection [label="执行反馈数据收集\n性能指标统计\n用户满意度评估\n四阶段自优化流程", fillcolor=lightblue];
        reward_function [label="强化学习奖励函数\nR = α·Quality + β·(1/ExecutionTime) + γ·(1/ResourceCost)", fillcolor=lightblue];
        gradient_descent [label="梯度下降参数更新\nAdam优化器\n学习率α=0.01\n超参数微调", fillcolor=lightblue];
        parameter_validation [label="参数验证与回滚\n交叉验证\n性能退化检测\n权重配置更新", fillcolor=lightblue];
    }

    // 异常处理机制
    subgraph cluster_exception_handling {
        label="异常处理与降级机制";
        style=filled;
        color=mistyrose;

        exception_detection [label="异常检测\n执行计划缺失\n权重计算异常\n性能下降检测", fillcolor=pink];
        fallback_mechanisms [label="降级机制\n默认γ系数\n历史统计备用配置\n自动参数校正", fillcolor=pink];
    }

    // 最终输出
    final_results [label="最终查询结果\n语义匹配准确\n执行效率优化\n跨模态理解能力", fillcolor=lightgreen];

    // 主要连接关系

    // 输入层到解析层
    structured_query -> query_parsing_module;
    api_interface -> query_parsing_module;
    natural_language -> query_parsing_module;

    // 查询解析到语义编码
    query_parsing_module -> mixed_query_processing;
    mixed_query_processing -> semantic_vector_generation;

    // 语义编码到USR模型
    semantic_vector_generation -> text_encoder;
    semantic_vector_generation -> image_encoder;
    semantic_vector_generation -> audio_encoder;
    semantic_vector_generation -> video_encoder;

    // SEncoder到统一语义向量
    text_encoder -> unified_semantic_vector;
    image_encoder -> unified_semantic_vector;
    audio_encoder -> unified_semantic_vector;
    video_encoder -> unified_semantic_vector;

    // 统一语义向量到MFusion融合器
    unified_semantic_vector -> three_factor_calculation;
    three_factor_calculation -> weight_normalization;
    weight_normalization -> fusion_formula;

    // 跨模态语义对齐
    unified_semantic_vector -> cross_modal_alignment;
    cross_modal_alignment -> fusion_formula;

    // 查询优化流程
    fusion_formula -> semantic_aware_analysis;
    semantic_aware_analysis -> intelligent_index_selection;
    intelligent_index_selection -> execution_plan_generation;
    execution_plan_generation -> postgresql_integration;

    // 查询执行
    postgresql_integration -> text_retrieval_path;
    postgresql_integration -> image_retrieval_path;
    postgresql_integration -> cross_modal_path;

    // 执行路径到动态调整
    text_retrieval_path -> dynamic_weight_adjustment;
    image_retrieval_path -> dynamic_weight_adjustment;
    cross_modal_path -> dynamic_weight_adjustment;

    // 语义搜索
    text_retrieval_path -> semantic_similarity_search;
    image_retrieval_path -> semantic_similarity_search;
    cross_modal_path -> semantic_similarity_search;

    // 数据存储访问
    semantic_similarity_search -> data_preprocessing;
    data_preprocessing -> semantic_vector_storage;
    semantic_vector_storage -> vector_indexing;
    vector_indexing -> cross_modal_cache;

    // 结果融合流程
    semantic_similarity_search -> weighted_fusion_scoring;
    cross_modal_cache -> weighted_fusion_scoring;
    weighted_fusion_scoring -> semantic_aware_sorting;
    semantic_aware_sorting -> execution_feedback_fusion;
    execution_feedback_fusion -> final_results;

    // 自优化反馈循环
    final_results -> feedback_collection;
    feedback_collection -> reward_function;
    reward_function -> gradient_descent;
    gradient_descent -> parameter_validation;
    parameter_validation -> three_factor_calculation [style=dashed, color=blue, label="权重更新"];

    // 动态权重调整反馈
    dynamic_weight_adjustment -> three_factor_calculation [style=dashed, color=green, label="实时调整"];

    // 异常处理连接
    three_factor_calculation -> exception_detection [style=dashed, color=red];
    exception_detection -> fallback_mechanisms;
    fallback_mechanisms -> three_factor_calculation [style=dashed, color=orange, label="降级处理"];

    // 关键技术创新标注
    subgraph cluster_innovations {
        label="四大技术创新点";
        style=filled;
        color=gold;

        innovation1 [label="USR模型\n统一语义表示\nSEncoder+MFusion", fillcolor=yellow];
        innovation2 [label="执行计划感知\n动态权重自适应\n三因子计算模型", fillcolor=yellow];
        innovation3 [label="语义感知优化\n跨模态查询支持\n智能索引选择", fillcolor=yellow];
        innovation4 [label="自优化机制\n强化学习驱动\n持续性能改进", fillcolor=yellow];
    }

    // 创新点与系统组件的关联
    innovation1 -> unified_semantic_vector [style=dashed, color=gold];
    innovation2 -> three_factor_calculation [style=dashed, color=gold];
    innovation3 -> execution_plan_generation [style=dashed, color=gold];
    innovation4 -> gradient_descent [style=dashed, color=gold];
}
