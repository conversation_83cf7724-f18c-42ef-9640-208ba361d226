# PostgreSQL 查询处理流程与执行计划详解

在 PostgreSQL 中，一条查询语句的完整执行流程一般如下：

1. 客户端将 SQL 查询发送给服务端
2. 解析（`Parser`）：服务端解析 SQL 语句，生成语法树
3. 分析（`Analyzer`）：检查语义合法性，生成查询树
4. 重写（`Rewriter`）：对查询树重写并生成新的查询树，以提供对规则和视图的支持
5. 优化（`Planner`）：生成多种执行路径，选择最优路径生成执行计划
6. 执行（`Executor`）：执行器执行计划并返回结果
7. 服务端将查询结果返回给客户端

## 一、查询编译

数据库管理系统中，对性能影响最大的是查询处理器。查询处理器是数据库管理系统中的一个部件集合，它允许用户使用 SQL 语言在较高层次上表达查询，其主要职责是将用户的各种命令转化成数据库上的操作序列并执行。查询处理分以下两阶段：

1. 查询编译：根据用户的查询语句生成数据库中最优执行计划，在此过程中要考虑视图、规则以及表的连接路径等问题。查询编译包括：
   - 查询分析器：负责词法分析、语法分析和语义分析，将 SQL 转换为内部表示形式
   - 查询预处理器：进行常量折叠、子查询处理等优化准备工作
   - 查询优化器：规划器/优化器的任务是创建一个优化了的执行计划。它首先生成完成查询所有可能的路径。这样创建的所有路径都产生相同的查询结果，而优化器的任务就是计算每个路径的开销并且找出开销最小的那条路径。
2. 查询执行：主要考虑执行计划时所采用的算法等问题，包括表扫描方法、连接算法、排序技术等

当 PostgreSQL 的后台服务进程 `Postgres` 接收到查询语句后：

![查询语句处理流程图](./images/查询语句处理流程图.png)

1. 首先将其传递到**查询分析**模块，进行词法、语法和语义分析
   1. 简单的命令（例如建表、创建用户、备份等）则将其分配到**功能性命令处理**模块
   2. 复杂的命令（`SELECT`/`INSERT`/`DELETE`/`UPDATE`）则要为其构建查询树（`Query` 结构体），然后交给**查询重写**模块。
2. **查询重写**模块接收到查询树后，按照该查询所涉及的规则和视图对查询树进行重写，生成新的查询树，然后将重写后的查询树传递给**查询规划**模块。
3. **查询规划**模块依据重写过的查询树，考虑关系的访问方式、连接方式和连接顺序等问题，采用动态规划算法或遗传算法，生成==最优路径==。
4. 最后，由最优路径生成可执行的计划，并将其传递到**查询执行**模块执行。

### 1.1 查询分析

将用户输入的 `SQL` 命令转换为查询树（`Query` 结构）。查询分析包括词法分析、语法分析和语义分析三个部分。其中词法分析和语法分析分别借助词法分析工具 `Lex` 和语法分析工具 `Yacc` 来完成各自的工作。

用户输入的 `SQL` 传递给查询分析器，通过词法和语法分析生成分析树，然后进行语义分析得到查询树。查询分析的基本流程如图所示：

![查询分析](./images/查询分析.png)

查询分析中主要的函数调用关系：

![查询分析函数调用](./images/查询分析函数调用.png)

1. 对于用户的 `SQL` 命令，统一由 `exec_simple_query` 函数处理
2. `exec_simple_query` 调用函数 `pg_parse_query` 进入词法和语法分析的主体处理过程
3. `pg_parse_query` 调用词法和语法分析的入口函数 `raw_parser` 生成分析树（原始分析树链表 `raw_parse_tree_list`）
4. `pg_parse_query` 返回分析树给外部函数。
5. `exec_simple_query` 接着调用函数 `pg_analyze_and_rewrite` 进行语义分析和查询重写。首先调用函数 `parse_analyze` 进行语义分析并生成查询树（用 `Query` 结构体表示），之后会将查询树传递给函数 `pg_rewrite_query` 进行查询重写

#### 1.1.1 词法分析

- 目标：将原始的 `SQL` 查询字符串分解为一个个有意义的单元（称为 `token`）
- 过程：
   - 输入：原始 `SQL` 查询文本，例如 `SELECT * FROM users WHERE age > 30;`
   - 输出：词法单元（`tokens`），比如 `SELECT`、`*`、`FROM`、`users`、`WHERE`、`age`、`>`、`30`、`;` 等
 - 作用：识别并分类 `SQL` 查询中的各种元素（关键字、标识符、常量、操作符等），并为后续的语法分析做准备
 - 实现：PostgreSQL 使用 `flex`（`lex` 的改进版）生成词法分析器，定义在 `scan.l` 文件中

#### 1.1.2 语法分析

- 目标：根据 `SQL` 的语法规则，检查查询是否符合语法，并将词法分析的结果（`tokens`）转换成抽象语法树（`AST`）或解析树（`Parse Tree`）
- 过程：
  - 输入：词法分析后的 `tokens`
  - 输出：抽象语法树（`AST`）或解析树。解析树是一个树形结构，表示 `SQL` 查询的语法结构
- 作用：确保 `SQL` 查询的结构是合法的，能被数据库引擎理解。它还帮助后续的查询重写和优化阶段
- 实现：PostgreSQL 使用 `bison`（`yacc` 的改进版）生成语法分析器，定义在 `gram.y` 文件中

#### 1.1.3 语义分析

语义分析是查询分析的最后一个阶段，主要负责验证查询的逻辑和业务规则是正确的，进一步保证查询能够在数据库中正确执行。

- 目标：确保 `SQL` 查询的逻辑和语义是正确的，验证查询中的标识符、数据类型、权限等是否符合数据库规则
- 过程：
  - 输入：通过词法分析和语法分析后生成的抽象语法树（`AST`）或解析树（`Parse Tree`）
  - 输出：一个语义正确且符合业务规则的查询树（`Query Tree`），这是一个更结构化的内部表示形式
- 主要任务
  1. 名称解析（`Name Resolution`）：
     - 目标：确保查询中的表名、列名、函数名等标识符在数据库中有正确的定义
     - 过程：查找查询中的每个标识符，并验证它们是否存在于数据库的系统目录中。例如，确保 `users` 表存在且列 `age` 在其中
     - 实现：通过查询系统目录表（如 `pg_class`、`pg_attribute` 等）来验证对象存在性
  2. 类型检查（`Type Checking`）：
     - 目标：确保查询中的数据类型兼容，不发生类型错误
     - 过程：检查查询中各个操作符、表达式的类型是否一致，并且符合数据库的数据类型约定。例如，验证 `age` 列为整数类型，而 `name` 列为字符串类型
     - 实现：PostgreSQL 会检查表达式中的操作数类型，必要时进行隐式类型转换
  3. 权限验证（`Permission Checking`）：
     - 目标：验证执行查询的用户是否拥有必要的权限来访问相关的数据库对象
     - 过程：检查用户是否有访问表、执行查询、更新数据等权限。例如，用户是否可以访问 `users` 表或执行 `SELECT` 操作
     - 实现：通过检查 `pg_authid`、`pg_auth_members` 等系统表中的权限信息
  4. 常量折叠（`Constant Folding`）：
     - 目标：在查询中包含常量表达式的情况下，提前计算常量值
     - 过程：将查询中的常量表达式提前进行计算，优化查询。例如，将 `30 + 5` 计算为 `35`，从而简化查询条件为 `age > 35`
     - 实现：在语义分析阶段，PostgreSQL 会识别并计算那些可以在编译时确定的表达式
  5. 表达式验证（`Expression Validation`）：
     - 目标：验证查询中的条件和表达式是否符合数据库的语义规则
     - 过程：检查查询中的表达式是否有效，比如验证 `WHERE` 子句中的条件是否能适用于表中的列。检查子查询是否符合语义要求
     - 实现：通过一系列语义规则检查，确保表达式在逻辑上是有效的

### 1.2 查询重写

查询重写是 PostgreSQL 查询处理流程中的一个重要阶段，它在语义分析之后、查询规划之前执行。

- 目标：根据数据库的规则和视图对查询树进行重写，生成新的查询树
- 过程：
  - 输入：通过语义分析后生成的查询树
  - 输出：一个重写后的查询树
- 主要任务
  1. 规则应用（`Rule Application`）：
     - 目标：根据数据库中定义的规则（通过 `CREATE RULE` 语句创建）对查询树进行转换
     - 过程：检查查询树中的每个节点，如果节点匹配某个规则的条件，则应用该规则进行重写
     - 实现：PostgreSQL 的规则系统允许用户定义在特定表上执行特定操作时应该执行的替代操作
     - 例子：可以定义一个规则，将对某个表的 `INSERT` 操作转换为对另一个表的 `INSERT` 操作，实现数据分发
  2. 视图展开（`View Expansion`）：
     - 目标：将查询中引用的视图替换为视图的定义
     - 过程：当查询引用视图时，查询重写器会将视图名替换为视图的定义查询
     - 实现：在 PostgreSQL 中，视图实际上是通过规则系统实现的特殊规则
     - 例子：如果有一个视图 `active_users` 定义为 `SELECT * FROM users WHERE status = 'active'`，那么查询 `SELECT * FROM active_users` 会被重写为 `SELECT * FROM users WHERE status = 'active'`
  3. 子查询处理（`Subquery Processing`）：
     - 目标：优化查询树中的子查询，提高查询效率
     - 过程：检查查询树中的子查询，尝试将其转换为更高效的形式
     - 实现：PostgreSQL 可以将某些类型的子查询转换为连接操作，这通常更高效
     - 例子：将 `SELECT * FROM orders WHERE customer_id IN (SELECT id FROM customers WHERE region = 'Europe')` 转换为 `SELECT orders.* FROM orders JOIN customers ON orders.customer_id = customers.id WHERE customers.region = 'Europe'`
  4. 继承表处理（`Inheritance Processing`）：
     - 目标：处理查询中涉及的继承表
     - 过程：当查询引用一个有子表的父表时，查询重写器会将查询扩展为包含所有子表的 `UNION ALL` 查询
     - 实现：通过检查系统目录中的继承关系，自动扩展查询
     - 例子：如果 `employees` 表有子表 `managers` 和 `staff`，那么 `SELECT * FROM employees` 会被重写为 `SELECT * FROM employees UNION ALL SELECT * FROM managers UNION ALL SELECT * FROM staff`

### 1.3 查询规划

在数据库管理系统中，用户的查询请求可以采用不同的方案来执行。尽管不同方案返回给用户的结果相同，但执行效率却存在差异，查询规划就用于选择一种代价最小的执行方案。因此，查询规划在数据库的查询性能方面起着举足轻重的作用。

查询分析工作完成之后，其最终产物——查询树链表将被移交给查询规划模块。查询规划模块将根据查询树链表生成查询计划，并选择一种代价最小的执行方案。

- 目标：选择一种代价最小的执行方案
- 过程：
   1. 预处理：对查询树（`Query` 结构体）的进一步改造，这种改造可通过 `SQL` 语句体现。在此过程中，最重要的是提升子链接和提升子查询
   2. 生成路径：接收到改造后的查询树后，采用动态规划算法或遗传算法，生成最优连接路径和候选的路径链表
   3. 生成计划：用得到的的最优路径，首先生成基本计划树（查询语句的 `SELECT ... FROM ... WHERE` 部分），然后添加 `GROUP BY`、`HAVING` 和 `ORDER BY` 等子句所对应的计划节点形成完整计划树

通常认为从 `planner` 函数开始就进入了执行计划的生成阶段。`planner` 函数的处理流程和函数调用关系如图所示：

![查询规划函数调用](./images/查询规划函数调用.png)

1. **`planner` 函数**：查询规划的入口点，调用 `standard_planner` 进行标准查询规划处理

2. **`standard_planner` 函数**：
   - 输入：`Query` 查询树和外部参数信息
   - 输出：`PlannedStmt` 结构体（包含执行器所需的全部信息）
   - 主要通过调用 `subquery_planner` 和 `set_plan_references` 完成计划生成、优化与清理

3. **`subquery_planner` 函数**：
   - 输入：`Query` 查询树
   - 输出：`Plan` 计划树
   - 功能：对查询树进行预处理，为子查询生成子计划树
   - 预处理原则：消除冗余条件、减少递归层数、简化路径生成
   - 遵循"先做选择，后做连接"的优化思想

4. **`inheritance_planner`/`grouping_planner` 函数**：
   - 根据表是否存在继承关系选择调用不同函数
   - `inheritance_planner` 将继承表处理成非继承关系表，然后调用 `grouping_planner`
   - `grouping_planner` 不再对查询树做变换，而是规范化信息并传递给 `query_planner`

5. **`query_planner` 函数**：
   - 调用 `make_one_rel` 函数（生成路径的主入口）
   - 生成代价最小路径（`cheapest_path`）和排序路径（`sorted_path`）
   - 通过 `get_cheapest_fractional_path_for_pathkeys` 在路径链表中寻找符合排序需求的路径

6. **最终计划生成**：
   - `grouping_planner` 从 `cheapest_path` 和 `sorted_path` 中确定最优路径 `best_path`
   - 调用 `create_plan` 为路径生成基本计划树
   - 根据 `GROUP BY` 和 `ORDER BY` 等条件添加相应计划节点，生成完整计划树

#### 1.3.1 代价估计

在 PostgreSQL 的查询规划过程中，查询请求的不同执行方案是通过建立不同的路径 `Path` 来表达的。在生成了许多符合条件的路径之后，要从中选择出代价最小的路径，把它转化为一个计划，传递给执行器执行。因此，规划器的核心工作就是建立多条路径，然后从中找出最优的那一条。同一个查询请求有不同路径主要是因为：表的不同访问方式、表之间不同的连接方式、表之间不同的连接顺序等因素造成的。而评价路径优劣的依据是用系统 `pg_statistic` 中的系统统计信息估计出的不同路径的代价（`Cost`）。

某个路径的代价要考虑 `CPU` 代价和磁盘存取代价两方面。磁盘代价以从磁盘顺序存取一个页面的代价为单位，所有其他形式的代价计算都是相对磁盘存取代价来计算的。

代价估算从 `I/O` 次数和 `CPU` 开销两个方面考虑，估算公式为 `P + W * T`。其中，`P` 表示在执行时所要访问的页面数，反映了磁盘 `I/O` 次数；`T` 表示在执行时所要访问的元组数，反映了 `CPU` 开销；`W` 表示磁盘 `I/O` 代价和 `CPU` 开销间的权重因子。通过对估算公式的分析可以发现，计算代价时只需考虑访问的页面数和元组数（**注意：这是一个概念上的简化公式，实际的代价模型更为复杂，会综合考虑下方提到的统计信息、配置参数以及更细致的操作成本**）。

PostgreSQL 中的代价估计还考虑了以下几个重要因素：

1. **统计信息**：
   - PostgreSQL 通过 `ANALYZE` 命令收集表和索引的统计信息，存储在 `pg_statistic` 系统表中
   - 这些统计信息包括表的大小、列的值分布、最常见值、直方图等
   - 优化器使用这些统计信息来估计查询条件的选择性和结果集大小

2. **配置参数**：
   - `seq_page_cost`：顺序读取一个磁盘页面的成本（默认为1.0）
   - `random_page_cost`：随机读取一个磁盘页面的成本（默认为4.0）
   - `cpu_tuple_cost`：处理一个元组的 `CPU` 成本（默认为0.01）
   - `cpu_index_tuple_cost`：处理一个索引元组的 `CPU` 成本（默认为0.005）
   - `cpu_operator_cost`：执行一个操作符或函数的 `CPU` 成本（默认为0.0025）
   - `effective_cache_size`：估计可用于磁盘缓存的内存大小

3. **启动成本与总成本**：
   - 启动成本（`startup cost`）：获取第一行结果前的成本
   - 总成本（`total cost`）：获取所有结果的总成本
   - 优化器会根据查询类型（如 `LIMIT` 查询）决定是优先考虑启动成本还是总成本

## 二、查询执行

查询编译器将用户提交的 `SQL` 查询语句转变为执行计划之后，由查询执行器继续执行查询的处理过程。同查询编译器一样，查询执行器也是被函数 `exec_simple_query` 调用，只是调用顺序上查询编译器在前，查询执行器在后。从总体上看，查询执行器实际就是按照执行计划的安排，有机地调用存储、索引、并发等模块，按照各种执行计划中的计划节点来实现数据的读取或修改的过程。

### 2.1 执行器概述

PostgreSQL 的执行器是查询处理的最后阶段，**负责按照优化器生成的执行计划获取或修改数据**。执行器接收一个完整的执行计划树（`Plan Tree`），然后**递归**地执行每个计划节点，最终产生查询结果。

执行器的主要职责包括：

1. **计划解释与执行**：解释执行计划树中的每个节点，并按照指定的操作执行
2. **数据流管理**：控制数据在不同计划节点之间的流动
3. **资源管理**：管理执行过程中的内存分配和释放
4. **并发控制**：确保在多用户环境下的数据一致性
5. **结果返回**：将查询结果返回给客户端

执行器与其他模块的关系：
- 接收来自优化器的执行计划
- 调用存储引擎获取表数据
- 使用缓冲区管理器进行数据缓存
- 与事务管理器协作确保事务的 `ACID` 属性
- 通过结果处理器将结果返回给客户端

### 2.2 执行器工作流程

![查询执行框架](./images/查询执行器框架.png)

PostgreSQL 执行器的工作流程主要包括以下几个步骤：

1. **初始化**：
   - 函数 `ExecutorStart` 初始化执行状态
   - 为执行计划分配必要的资源和内存
   - 打开所需的关系（表）
   - 初始化执行参数

2. **执行**：
   - 函数 `ExecutorRun` 开始执行计划
   - 采用"火山模型"（`Volcano Model`）的迭代执行方式
   - 通过 `ExecProcNode` 函数递归调用计划树中的每个节点
   - 每个节点通过 `ExecProcNode` 返回下一个元组（行）

3. **清理**：
   - 函数 `ExecutorFinish` 处理任何需要在返回结果前完成的操作
   - 函数 `ExecutorEnd` 释放所有分配的资源
   - 关闭打开的关系

执行器的核心是 `ExecProcNode` 函数，它根据节点类型调用相应的执行函数：

```c
TupleTableSlot *
ExecProcNode(PlanState *node)
{
    if (node == NULL)
        return NULL;

    switch (nodeTag(node))
    {
        case T_SeqScanState:
            return ExecSeqScan((SeqScanState *) node);
        case T_IndexScanState:
            return ExecIndexScan((IndexScanState *) node);
        case T_NestLoopState:
            return ExecNestLoop((NestLoopState *) node);
        case T_HashJoinState:
            return ExecHashJoin((HashJoinState *) node);
        // ... 其他节点类型
    }
}
```

### 2.3 执行节点类型与实现

PostgreSQL 执行器支持多种执行节点类型，每种节点负责特定类型的操作。以下是主要节点类型及其实现原理：

#### 2.3.1 扫描节点

扫描节点负责从表或索引中检索数据。

1. **顺序扫描（`Sequential Scan`）**：
   - 实现函数：`ExecSeqScan`
   - 工作原理：顺序读取表的所有数据块，对每一行应用过滤条件
   - 关键步骤：
     1. 初始化表扫描描述符
     2. 使用 `heap_getnext` 获取下一个满足条件的元组
     3. 将元组放入结果槽（`TupleTableSlot`）

2. **索引扫描（`Index Scan`）**：
   - 实现函数：`ExecIndexScan`
   - 工作原理：通过索引定位满足条件的行，然后访问表获取完整数据
   - 关键步骤：
     1. 使用索引条件定位匹配的索引项
     2. 获取对应的表位置（`TID`）
     3. 访问表获取完整行数据

3. **索引仅扫描（`Index Only Scan`）**：
   - 实现函数：`ExecIndexOnlyScan`
   - 工作原理：仅从索引中获取所需数据，不访问表
   - 优化：利用可见性映射（`Visibility Map`）检查行可见性

4. **位图扫描（`Bitmap Scan`）**：
   - 实现函数：`ExecBitmapHeapScan` 和 `ExecBitmapIndexScan`
   - 工作原理：两阶段扫描，先创建位图标记匹配行，再按物理顺序访问表
   - 优势：减少随机I/O，提高磁盘访问效率。**特别适用于索引选择性中等，能过滤掉大部分行，但匹配行在物理上仍较为分散的情况。通过位图，将潜在的大量随机磁盘访问转换为更有序的批量访问。**

#### 2.3.2 连接节点

连接节点负责将多个表的数据按照指定条件组合在一起。

1. **嵌套循环连接（`Nested Loop Join`）**：
   - 实现函数：`ExecNestLoop`
   - 工作原理：对外表的每一行，扫描内表查找匹配行
   - 伪代码：
     ```
     for each row R1 in outer table
         for each row R2 in inner table
             if join_condition(R1, R2) then
                 output combined row
     ```
   - 适用场景：小表连接或连接条件有高效索引

2. **哈希连接（`Hash Join`）**：
   - 实现函数：`ExecHashJoin`
   - 工作原理：
     1. 构建阶段：将较小表的连接键和行数据构建哈希表
     2. 探测阶段：扫描较大表，查找哈希表中的匹配项
   - 内存管理：使用 `work_mem` 参数控制哈希表大小，超出时分批处理
   - 适用场景：大表连接且连接条件无索引

3. **合并连接（`Merge Join`）**：
   - 实现函数：`ExecMergeJoin`
   - 工作原理：同时扫描两个已排序的输入，合并匹配的行
   - 前提条件：输入必须按连接键排序
   - 适用场景：已排序的数据或排序代价较低

#### 2.3.3 聚合和排序节点

1. **聚合节点（`Aggregate`）**：
   - 实现函数：`ExecAgg`
   - 工作原理：根据分组键计算聚合函数（`SUM`、`COUNT`、`AVG`等）
   - 实现方式：
     - 普通聚合：对所有输入行计算单个结果
     - 分组聚合：按 `GROUP BY` 子句分组计算
     - 哈希聚合：使用哈希表存储分组数据
     - 排序聚合：先排序再聚合相邻的相同组

2. **排序节点（`Sort`）**：
   - 实现函数：`ExecSort`
   - 工作原理：对输入数据按指定列排序
   - 内存管理：
     - 使用 `work_mem` 参数控制内存使用
     - 当数据超过 `work_mem` 时，使用外部排序算法
     - 创建临时文件存储中间结果

#### 2.3.4 其他特殊节点

1. **限制节点（`Limit`）**：
   - 实现函数：`ExecLimit`
   - 工作原理：限制返回的行数（`LIMIT`）或跳过指定行数（`OFFSET`）

2. **物化节点（`Materialize`）**：
   - 实现函数：`ExecMaterialize`
   - 工作原理：将子计划的结果缓存在内存中，避免重复计算
   - 使用场景：嵌套循环连接的内表、需多次扫描的子查询

3. **唯一节点（`Unique`）**：
   - 实现函数：`ExecUnique`
   - 工作原理：移除重复行，实现 `DISTINCT` 操作

4. **子计划节点（`SubPlan`）**：
   - 实现函数：`ExecSubPlan`
   - 工作原理：执行子查询，可能多次执行或缓存结果

### 2.4 执行策略

PostgreSQL 采用多种执行策略来提高查询性能：

#### 2.4.1 火山模型执行策略

PostgreSQL 主要采用"火山模型"（`Volcano Model`）或称为"迭代器模型"（`Iterator Model`）的执行策略：

- **基本原理**：每个执行节点实现一个 `next()` 方法（在PostgreSQL中是各种 `ExecXXX` 函数），返回下一个元组
- **数据流**：数据自底向上流动，每次请求一个元组
- **优点**：
  - 实现简单，容易扩展
  - 内存使用效率高，适合OLTP工作负载
  - 支持流式处理，不需要缓存全部结果
- **缺点**：
  - 函数调用开销大
  - 不利于现代CPU的缓存和流水线优化
  - 对于大量数据的分析查询效率较低

#### 2.4.2 批处理执行优化

为了克服火山模型的缺点，PostgreSQL在某些场景下采用批处理优化：

- **批量获取**：一次获取多个元组而不是单个元组
- **向量化处理**：对多个元组同时应用相同操作
- **应用场景**：
  - 外部数据包装器（`Foreign Data Wrapper`）
  - 并行查询执行
  - 某些特定操作（如聚合）

#### 2.4.3 并行执行

PostgreSQL 9.6及以后版本支持并行查询执行：

- **并行扫描**：多个工作进程同时扫描表的不同部分
- **并行连接**：在多个工作进程间分配连接操作
- **并行聚合**：多个工作进程执行部分聚合，然后合并结果
- **实现机制**：
  - 使用后台工作进程（`Background Worker`）
  - 通过共享内存进行进程间通信
  - 动态调整并行度（基于系统负载和查询复杂度）

### 2.5 执行器优化技术

#### 2.5.1 执行时重优化

PostgreSQL 在执行过程中可能进行重优化：

- **自适应执行**：根据实际数据特征调整执行策略
- **动态过滤下推**：将过滤条件动态下推到扫描节点
- **参数化路径**：为参数化查询生成通用执行计划

#### 2.5.2 内存管理优化

- **内存上下文**：使用分层内存上下文管理内存分配和释放
- **工作内存控制**：通过 `work_mem` 参数控制排序、哈希等操作的内存使用
- **元组存储优化**：使用 `TupleTableSlot` 高效存储和传递元组

#### 2.5.3 I/O优化

- **预读取**：预先读取可能需要的数据块
- **批量I/O**：一次读取多个数据块
- **缓冲区管理**：高效利用共享缓冲区和操作系统缓存
- **异步I/O**：在某些场景下使用异步I/O减少等待时间

### 2.6 执行器与事务管理

执行器与事务管理系统紧密协作，确保数据一致性：

- **可见性检查**：执行器只处理对当前事务可见的数据
- **锁管理**：根据隔离级别获取适当的锁
- **MVCC支持**：通过多版本并发控制实现非阻塞读取
- **错误处理**：在执行过程中出现错误时正确回滚事务

## 三、EXPLAIN 命令

PostgreSQL 为每个收到查询产生一个查询计划。选择正确的计划来匹配查询结构和数据的属性对于好的性能来说绝对是最关键的，因此系统包含了一个复杂的规划器来尝试选择好的计划。使用 `EXPLAIN` 命令察看规划器为任何查询生成的查询计划。

`EXPLAIN` 命令是 PostgreSQL 提供的一个用于查看查询计划和性能分析的工具。它可以帮助开发者理解和优化查询性能，特别是在进行性能调优时。

### 3.1 基本用法

`EXPLAIN` 命令的基本语法如下：

```sql
EXPLAIN [ ( option [, ...] ) ] statement
EXPLAIN [ ANALYZE ] [ VERBOSE ] statement

这里 option可以是：
    ANALYZE [ boolean ]
    VERBOSE [ boolean ]
    COSTS [ boolean ]
    BUFFERS [ boolean ]
    TIMING [ boolean ]
    SUMMARY [ boolean ]
    FORMAT { TEXT | XML | JSON | YAML }
```

各个参数的含义如下：

- **`ANALYZE`**：执行命令并显示实际运行时间和其他统计信息。因为被真正执行过，所以可以看到执行计划每一步耗费了多长时间，以及它实际返回的行数。尽管 `EXPLAIN` 将丢弃 `SELECT` 所返回的任何输出，该语句的其他副作用还是会发生，所以建议在一个事务中执行 `EXPLAIN DML`。
  - **`BUFFERS`**：缓冲区使用的信息：共享块命中、读取、标记为脏和写入的次数、本地块命中、读取、标记为脏和写入的次数、以及临时块读取和写入的次数。只有当 `ANALYZE` 也被启用时，这个参数才能使用。
  - **`TIMING`**：在输出中包括实际启动时间以及在每个结点中花掉的时间。只有当 `ANALYZE` 也被启用时，这个参数才能使用。

- **`VERBOSE`**：显示规划树完整的内部表现形式，将输出的执行计划精确到列级别。通常，这个选项只是在特殊的调试过程中有用。`VERBOSE` 输出是否打印工整，具体取决于配置参数 `explain_pretty_print` 的值。

- **`COSTS`**：每一个计划结点的估计启动和总代价，以及估计的行数和每行的宽度。例如 `(cost=0.00..458.00 rows=10000 width=244)` 从左到右依次表示：
  - 启动开销。执行该步骤前的初始成本，例如在一个排序结点里执行排序的时间。
  - 总开销。这个估计值基于的假设是计划结点会被运行到完成。不过实际上一个结点的父结点可能很快停止读所有可用的行（`LIMIT`）。
  - 此计划结点输出行数的估计值。假定该结点能运行到完成。
  - 此计划结点输出的行平均宽度（以字节计算）。

- **`SUMMARY`**：在查询计划之后包含摘要信息（例如，总计的时间信息）。当使用 `ANALYZE` 时默认包含摘要信息，但默认情况下不包含摘要信息，但可以使用此选项启用摘要信息。使用 `EXPLAIN EXECUTE` 中的计划时间包括从缓存中获取计划所需的时间以及重新计划所需的时间（如有必要）。

- **`FORMAT`**：指定输出格式，可以是 `TEXT`、`XML`、`JSON` 或者 `YAML`。非文本输出包含和文本输出格式相同的信息，但是更容易被程序解析。这个参数默认被设置为 `TEXT`。

- **`statement`**：执行的 SQL 查询或操作的语句本身。

### 3.2 执行计划节点详解

`EXPLAIN` 输出中的每个节点代表查询执行的一个操作。以下是常见节点的详细解释：

#### 3.2.1 扫描节点

- **`Seq Scan`**：顺序扫描表的所有数据块。
  ```
  Seq Scan on table_name  (cost=0.00..433.00 rows=10000 width=244)
    Filter: (column_name > 100)
  ```
  - 当表较小或查询条件无法使用索引时使用
  - `Filter` 表示在扫描过程中应用的过滤条件

- **`Index Scan`**：通过索引查找数据，然后访问表获取完整行。
  ```
  Index Scan using idx_name on table_name  (cost=0.29..8.30 rows=1 width=244)
    Index Cond: (id = 123)
  ```
  - 适用于高选择性查询
  - `Index Cond` 表示用于索引查找的条件

- **Index Only Scan**：只从索引中获取数据，不访问表。
  ```
  Index Only Scan using idx_name on table_name  (cost=0.29..8.30 rows=1 width=8)
    Index Cond: (id = 123)
  ```
  - 当查询只需要索引中包含的列时使用
  - 比 Index Scan 更高效

- **Bitmap Scan**：两阶段扫描，先创建位图，再按顺序访问表。
  ```
  Bitmap Heap Scan on table_name  (cost=4.65..13.23 rows=10 width=244)
    Recheck Cond: (column_name > 100 AND column_name < 200)
    ->  Bitmap Index Scan on idx_name  (cost=0.00..4.65 rows=10 width=0)
          Index Cond: (column_name > 100 AND column_name < 200)
  ```
  - 适用于中等选择性的查询
  - 比 Index Scan 更高效，因为减少了随机 I/O

#### 3.2.2 连接节点

- **Nested Loop**：嵌套循环连接，对外表的每一行，扫描内表。
  ```
  Nested Loop  (cost=0.29..14.40 rows=10 width=488)
    ->  Seq Scan on outer_table  (cost=0.00..1.10 rows=10 width=244)
    ->  Index Scan using idx_name on inner_table  (cost=0.29..1.32 rows=1 width=244)
          Index Cond: (id = outer_table.id)
  ```
  - 适用于小表连接或连接条件有高效索引的情况

- **Hash Join**：构建哈希表，然后探测匹配项。
  ```
  Hash Join  (cost=10.75..13.61 rows=10 width=488)
    Hash Cond: (outer_table.id = inner_table.id)
    ->  Seq Scan on outer_table  (cost=0.00..1.10 rows=10 width=244)
    ->  Hash  (cost=1.09..1.09 rows=10 width=244)
          ->  Seq Scan on inner_table  (cost=0.00..1.09 rows=10 width=244)
  ```
  - 适用于大表连接且连接条件无索引的情况
  - 需要足够内存来存储哈希表

- **Merge Join**：合并两个已排序的输入。
  ```
  Merge Join  (cost=4.98..6.56 rows=10 width=488)
    Merge Cond: (outer_table.id = inner_table.id)
    ->  Sort  (cost=2.33..2.58 rows=10 width=244)
          Sort Key: outer_table.id
          ->  Seq Scan on outer_table  (cost=0.00..1.10 rows=10 width=244)
    ->  Sort  (cost=2.33..2.58 rows=10 width=244)
          Sort Key: inner_table.id
          ->  Seq Scan on inner_table  (cost=0.00..1.09 rows=10 width=244)
  ```
  - 适用于已排序的数据或排序代价较低的情况

#### 3.2.3 其他常见节点

- **Sort**：对数据进行排序。
  ```
  Sort  (cost=2.33..2.58 rows=10 width=244)
    Sort Key: column_name
    ->  Seq Scan on table_name  (cost=0.00..1.10 rows=10 width=244)
  ```
  - 实现 ORDER BY 子句
  - Sort Key 表示排序的列

- **Aggregate**：执行聚合操作。
  ```
  HashAggregate  (cost=1.35..1.60 rows=10 width=36)
    Group Key: column_name
    ->  Seq Scan on table_name  (cost=0.00..1.10 rows=10 width=244)
  ```
  - 实现 GROUP BY 和聚合函数（SUM、COUNT 等）
  - Group Key 表示分组的列

- **Limit**：限制返回的行数。
  ```
  Limit  (cost=0.29..0.54 rows=5 width=244)
    ->  Index Scan using idx_name on table_name  (cost=0.29..8.30 rows=100 width=244)
          Index Cond: (id > 100)
  ```
  - 实现 LIMIT 子句
  - 可以显著减少执行时间

### 3.3 EXPLAIN 示例

```sql
CREATE TABLE employees (
    id SERIAL PRIMARY KEY,
    name TEXT,
    department TEXT,
    salary INT
);

INSERT INTO employees (name, department, salary) VALUES
('Alice', 'HR', 5000),
('Bob', 'Engineering', 8000),
('Charlie', 'HR', 5500),
('David', 'Engineering', 9000),
('Eve', 'Marketing', 6000);

postgres=# EXPLAIN SELECT * FROM employees;
                         QUERY PLAN                          
-------------------------------------------------------------
 Seq Scan on employees  (cost=0.00..18.10 rows=810 width=72)
(1 row)

postgres=# EXPLAIN SELECT * FROM employees WHERE department = 'Engineering';
                        QUERY PLAN                         
-----------------------------------------------------------
 Seq Scan on employees  (cost=0.00..20.12 rows=4 width=72)
   Filter: (department = 'Engineering'::text)
(2 rows)

```

找出 薪资大于 5000 的员工，与其参与的项目连接，计算每人总共投入的工时（SUM(hours_spent)），并按投入工时降序排序：

```sql
postgres=# EXPLAIN ANALYZE
SELECT e.name, e.department, SUM(p.hours_spent) AS total_hours
FROM employees e
JOIN projects p ON e.id = p.employee_id
WHERE e.salary > 5000
GROUP BY e.name, e.department
ORDER BY total_hours DESC;
                                                           QUERY PLAN                                                            
---------------------------------------------------------------------------------------------------------------------------------
 Sort  (cost=58.12..58.52 rows=161 width=72) (actual time=0.037..0.038 rows=4 loops=1)
   Sort Key: (sum(p.hours_spent)) DESC
   Sort Method: quicksort  Memory: 25kB
   ->  HashAggregate  (cost=50.60..52.21 rows=161 width=72) (actual time=0.029..0.031 rows=4 loops=1)
         Group Key: e.name, e.department
         Batches: 1  Memory Usage: 40kB
         ->  Hash Join  (cost=23.50..47.78 rows=377 width=68) (actual time=0.022..0.024 rows=5 loops=1)
               Hash Cond: (p.employee_id = e.id)
               ->  Seq Scan on projects p  (cost=0.00..21.30 rows=1130 width=8) (actual time=0.005..0.005 rows=7 loops=1)
               ->  Hash  (cost=20.12..20.12 rows=270 width=68) (actual time=0.010..0.010 rows=4 loops=1)
                     Buckets: 1024  Batches: 1  Memory Usage: 9kB
                     ->  Seq Scan on employees e  (cost=0.00..20.12 rows=270 width=68) (actual time=0.005..0.006 rows=4 loops=1)
                           Filter: (salary > 5000)
                           Rows Removed by Filter: 1
 Planning Time: 0.208 ms
 Execution Time: 0.070 ms
(16 rows)
```

逻辑执行步骤，解读上述 Plan（从内向外，自底向上看）：

1.  **Seq Scan on employees e**: 顺序扫描 `employees` 表，应用 `Filter: (salary > 5000)` 条件。
2.  **Hash**: 将上一步过滤后的 `employees` 表的连接键（id）和相关列构建成一个哈希表，存入内存（Memory Usage: 9kB）。
3.  **Seq Scan on projects p**: 顺序扫描 `projects` 表。
4.  **Hash Join**: 使用 `projects` 表的 `employee_id` 去探测第 2 步构建的哈希表 (`Hash Cond: (p.employee_id = e.id)`), 输出匹配的连接结果。
5.  **HashAggregate**: 对 Hash Join 的结果，按 `e.name` 和 `e.department` 进行分组 (`Group Key`)，并计算 `SUM(p.hours_spent)` 聚合值。使用哈希方式进行聚合（Memory Usage: 40kB）。
6.  **Sort**: 对聚合后的结果，根据 `total_hours` (`sum(p.hours_spent)`) 进行降序排序 (`Sort Key`, `DESC`)。使用快速排序算法（Memory: 25kB）。

### 3.4 优化建议

SQL 优化通常是一个迭代的过程：通过 `EXPLAIN` 分析当前执行计划 -> 识别瓶颈 -> 提出优化假设（如加索引、改写SQL、调整参数）-> 应用更改 -> 再次 `EXPLAIN ANALYZE` 验证效果。

根据 EXPLAIN 输出优化查询的一般步骤：

1. **识别高成本操作**：
   - 查找 cost 值最高的节点
   - 关注实际行数与估计行数差异大的节点

2. **优化表扫描**：
   - 如果 Seq Scan 处理大表，考虑添加索引
   - 确保 WHERE 子句中的条件列有适当的索引

3. **优化连接**：
   - 对于 Nested Loop Join，确保内表有连接键的索引
   - 对于 Hash Join，确保有足够的 work_mem

4. **优化排序**：
   - 为 ORDER BY 子句中的列创建索引
   - 增加 work_mem 参数值以避免磁盘排序

5. **优化分组和聚合**：
   - 为 GROUP BY 子句中的列创建索引
   - 考虑使用物化视图预计算聚合结果

通过定期分析查询计划并进行相应优化，可以显著提高 PostgreSQL 数据库的性能。
