# Datafusion 技术文档

## 一、Datafusion 架构

与传统高度耦合的系统不同，`Datafusion`的每个部分用户都可以自由扩展其功能特性，这样不仅可以最大化使用高效稳定的开源代码，也可以在不修改开源代码的情况下实现不同系统的定制功能，这就是一个组件类开源项目最大的优势。

下图是`Datafusion`的架构图：

![Datafusion架构图](./images/Datafusion架构图.png)

从图中可以看出，`Datafusion`的架构主要分为以下几个部分：

1. 目录和数据来源 (`Data Sources`)：多种数据格式，每种数据源都会被标准化为内部逻辑计划表示
2. 查询前端 (`Query FrontEnds`)：语法解析、语义分析和生成逻辑计划
3. 计划表示和重写 (`Plan Representations and Rewrites`)
  a. 逻辑计划 (`LogicalPlan`)：通过优化/转换 (`Optimizations/Transformations`)，生成高效的可执行的逻辑计划。
  b. 执行计划 (`ExecutionPlan`)：将逻辑计划转换为物理计划。执行计划通过进一步的优化和转换优化/转换 (`Optimizations/Transformations`)。
4. 执行器 (`Execution Engine`)：执行物理查询计划，生成查询结果。

## 二、Datafusion 逻辑计划

逻辑计划是对数据库查询的结构化表示，描述了从数据库或数据源检索数据所需的高级操作和转换。它抽象了具体的实现细节，专注于查询的逻辑流程，包括过滤、排序和表连接等操作。

`DataFusion`的逻辑计划是SQL查询或`DataFrame`操作的抽象表示，它描述了"要做什么"而不是"如何做"。逻辑计划实际上是一个由不同操作节点组成的`树形结构`，每个节点代表一种关系代数操作（如投影、过滤、连接等）。

逻辑计划的核心优势在于：
1. 与物理实现解耦 - `逻辑计划`只描述操作的语义，不关心具体算法实现
2. 优化机会 - 这种抽象设计使得系统可以在不改变查询语义的前提下重写和优化计划
3. 模块化设计 - 将查询处理分为解析、优化和执行等独立阶段

### 2.1 逻辑计划简介

`DataFusion`的`LogicalPlan`是一个枚举类型，包含所有支持的操作的变体，还包含一个`Extension`变体，允许基于`DataFusion`构建的项目添加自定义逻辑操作符：

```rust
pub enum LogicalPlan {
    // 基本关系代数操作
    Projection(Projection),   // 投影
    Filter(Filter),           // 过滤
    Window(Window),           // 窗口
    Aggregate(Aggregate),     // 聚合
    Sort(Sort),               // 排序
    Join(Join),               // 连接
    Repartition(Repartition), // 重新分区
    Union(Union),             // 联合
    
    // 数据源操作
    TableScan(TableScan),          // 表扫描
    EmptyRelation(EmptyRelation),  // 空关系
    Subquery(Subquery),            // 子查询
    SubqueryAlias(SubqueryAlias),  // 子查询别名
    
    // 结果集操作
    Limit(Limit),      // 限制
    Values(Values),    // 值
    Distinct(Distinct),// 去重
    
    // 元操作
    Explain(Explain),  // 解释
    Analyze(Analyze),  // 分析
    Extension(Extension),// 扩展
    
    // SQL语句类型
    Statement(Statement),  // 语句
    Dml(DmlStatement),     // 数据操纵语言
    Ddl(DdlStatement),     // 数据定义语言
    Copy(CopyTo),          // 复制
    
    // 表结构操作
    DescribeTable(DescribeTable),// 描述表
    
    // 高级操作
    Unnest(Unnest),             // 解嵌套
    RecursiveQuery(RecursiveQuery),// 递归查询
}
```

### 2.2 常见逻辑算子分析

#### 2.2.1 投影

投影算子用于选择和重新排列表中的列。它接收一个表扫描节点和一组列索引，并返回一个新的表扫描节点，其中只包含选定的列。

```rust
pub struct Projection {
    // 表达式列表
    pub expr: Vec<Expr>,
    // 输入计划
    pub input: Arc<LogicalPlan>,
    // 输出模式
    pub schema: DFSchemaRef,
}
```

投影可以简单地选择列（如`col("id")`），也可以包含复杂表达式（如`col("price").mul(col("quantity"))`）。

#### 2.2.2 表扫描

表扫描是逻辑计划中最常见的算子之一，用于从数据源中读取数据。它将数据源的物理表示转换为逻辑计划中的表扫描节点。

```rust
pub struct TableScan {
    // 表名
    pub table_name: TableReference,
    // 表数据源
    pub source: Arc<dyn TableSource>,
    // 可选的列投影（索引）
    pub projection: Option<Vec<usize>>,
    // 投影后的模式
    pub projected_schema: DFSchemaRef,
    // 可能由表提供者执行的过滤
    pub filters: Vec<Expr>,
    // 可选的读取行数限制
    pub fetch: Option<usize>,
}
```

`TableScan`支持列裁剪（只读取需要的列）和谓词下推（在数据源层面应用过滤）优化。

#### 2.2.3 过滤

过滤算子用于根据条件过滤数据。它接收一个表达式和一个表扫描节点，并返回一个新的表扫描节点，其中只包含满足条件的行。

```rust
pub struct Filter {
    // 谓词表达式（必须返回布尔类型）
    pub predicate: Expr,
    // 输入计划
    pub input: Arc<LogicalPlan>,
    // 标记是否为HAVING子句
    pub having: bool,
}
```

`Filter`算子支持谓词下推（在数据源层面应用过滤）优化。

#### 2.2.4 连接

连接算子用于将两个表按照某种条件进行合并。它接收两个表扫描节点和连接条件，并返回一个新的表扫描节点，其中包含连接后的数据。

```rust
pub struct Join {
    // 左输入
    pub left: Arc<LogicalPlan>,
    // 右输入
    pub right: Arc<LogicalPlan>,
    // 等值连接条件
    pub on: Vec<(Expr, Expr)>,
    // 非等值连接条件
    pub filter: Option<Expr>,
    // 连接类型（内连接、左外、右外等）
    pub join_type: JoinType,
    // 连接约束
    pub join_constraint: JoinConstraint,
    // 输出模式
    pub schema: DFSchemaRef,
    // null等于null的处理方式
    pub null_equals_null: bool,
}
```

`Join`算子支持连接类型（内连接、左外、右外等）和连接约束（等值、非等值、自然连接等）优化。

#### 2.2.5 聚合

聚合算子用于对数据进行统计分析。它接收一个表扫描节点和一组聚合函数，并返回一个新的表扫描节点，其中包含聚合后的数据。

```rust
pub struct Aggregate {
    // 输入计划
    pub input: Arc<LogicalPlan>,
    // 分组表达式
    pub group_expr: Vec<Expr>,
    // 聚合表达式
    pub aggr_expr: Vec<Expr>,
    // 输出模式
    pub schema: DFSchemaRef,
}
```

`Aggregate`算子支持分组和聚合函数优化。

#### 2.2.6 排序

排序算子用于对数据进行排序。它接收一个表扫描节点和一组排序表达式，并返回一个新的表扫描节点，其中包含排序后的数据。

```rust
pub struct Sort {
    // 排序表达式
    pub expr: Vec<SortExpr>,
    // 输入逻辑计划
    pub input: Arc<LogicalPlan>,
    // 可选的限制条件
    pub fetch: Option<usize>,
}
```

`Sort`算子支持排序和限制条件优化。

### 2.3 构建逻辑计划

通过创建`LogicalPlan`枚举实例来构建逻辑计划（如下所示）：

```rust
use datafusion::common::DataFusionError;
use datafusion::arrow::datatypes::{DataType, Field, Schema, SchemaRef};
use datafusion::logical_expr::{Filter, LogicalPlan, TableScan, LogicalTableSource};
use datafusion::prelude::*;
use std::sync::Arc;

fn main() -> Result<(), DataFusionError> {
    // 创建逻辑表源
    let schema = Schema::new(vec![
        Field::new("id", DataType::Int32, true),
        Field::new("name", DataType::Utf8, true),
    ]);
    let table_source = LogicalTableSource::new(SchemaRef::new(schema));

    // 创建表扫描计划
    let projection = None; // 可选投影
    let filters = vec![];  // 可推送的过滤条件
    let fetch = None;      // 可选LIMIT
    let table_scan = LogicalPlan::TableScan(TableScan::try_new(
        "person",
        Arc::new(table_source),
        projection,
        filters,
        fetch,
    )?);

    // 创建评估`id > 500`的过滤计划，包裹表扫描
    let filter_expr = col("id").gt(lit(500));
    let plan = LogicalPlan::Filter(Filter::try_new(filter_expr, Arc::new(table_scan))?);

    // 打印计划
    println!("{}", plan.display_indent_schema());
    Ok(())
}
```

该示例生成以下计划：

```bash
leolong@TH:examples(* main)$ cargo run --example test
   Compiling datafusion-examples v46.0.0 (/home/<USER>/code/datafusion/datafusion-examples)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 19.63s
     Running `/home/<USER>/code/datafusion/target/debug/examples/test`
Filter: id > Int32(500) [id:Int32;N, name:Utf8;N]
  TableScan: person [id:Int32;N, name:Utf8;N]
```

### 2.3 使用LogicalPlanBuilder构建逻辑计划

虽然可以直接通过创建`LogicalPlan`枚举实例来构建逻辑计划，但使用`LogicalPlanBuilder`会更加简便。

创建新构建器有多个函数可用，例如：

- `empty` - 创建无字段的空计划
- `values` - 从字面值集合创建计划
- `scan` - 创建表示表扫描的计划
- `scan_with_filters` - 创建带过滤条件的表扫描计划

构建器创建后，可以调用转换方法来声明应在计划上执行的进一步操作。注意，此阶段我们**仅构建逻辑计划结构，不会执行任何查询**。

部分转换方法示例：

- `filter`
- `limit`
- `sort`
- `distinct`
- `join`

以下示例演示了如何构建与前一示例相同的查询计划（表扫描后跟过滤条件）：

```rust
use datafusion::common::DataFusionError;
use datafusion::arrow::datatypes::{DataType, Field, Schema, SchemaRef};
use datafusion::logical_expr::{LogicalPlanBuilder, LogicalTableSource};
use datafusion::prelude::*;
use std::sync::Arc;

fn main() -> Result<(), DataFusionError> {
    // 创建逻辑表源
    let schema = Schema::new(vec![
        Field::new("id", DataType::Int32, true),
        Field::new("name", DataType::Utf8, true),
    ]);
    let table_source = LogicalTableSource::new(SchemaRef::new(schema));

    let projection = None; // 可选投影

    // 为表扫描创建LogicalPlanBuilder
    let builder = LogicalPlanBuilder::scan("person", Arc::new(table_source), projection)?;

    // 执行过滤操作并构建计划
    let plan = builder
        .filter(col("id").gt(lit(500)))? // WHERE id > 500
        .build()?;

    // 打印计划
    println!("{}", plan.display_indent_schema());
    Ok(())
}
```

### 2.4 逻辑计划转换

逻辑计划不能直接执行，必须"编译"为`ExecutionPlan`（通常称为"物理计划"）。相较于`LogicalPlan`，`ExecutionPlan`包含更多细节，如特定算法和详细优化。对于给定的`LogicalPlan`，创建`ExecutionPlan`最简单的方法是使用`SessionState::create_physical_plan`，如下所示：

```rust
use datafusion::datasource::{provider_as_source, MemTable};
use datafusion::common::DataFusionError;
use datafusion::physical_plan::display::DisplayableExecutionPlan;
use datafusion::arrow::datatypes::{DataType, Field, Schema};
use datafusion::logical_expr::LogicalPlanBuilder;
use datafusion::prelude::*;
use std::sync::Arc;

// 创建物理计划可能访问远程目录和数据源，因此必须在异步运行时中运行
#[tokio::main]
async fn main() -> Result<(), DataFusionError> {
    // 创建默认表源
    let schema = Schema::new(vec![
        Field::new("id", DataType::Int32, true),
        Field::new("name", DataType::Utf8, true),
    ]);
    // 要创建ExecutionPlan，必须提供实际的TableProvider
    // 本示例不提供数据，生产代码中应包含内存中的RecordBatch
    let table_provider = Arc::new(MemTable::try_new(Arc::new(schema), vec![])?);
    // 使用provider_as_source将TableProvider转换为表源
    let table_source = provider_as_source(table_provider);

    // 创建无投影和过滤条件的表扫描LogicalPlanBuilder
    let logical_plan = LogicalPlanBuilder::scan("person", table_source, None)?.build()?;

    // 通过调用create_physical_plan创建物理计划
    let ctx = SessionContext::new();
    let physical_plan = ctx.state().create_physical_plan(&logical_plan).await?;

    // 打印计划
    println!("{}", DisplayableExecutionPlan::new(physical_plan.as_ref()).indent(true));
    Ok(())
}
```

该示例生成以下计划：

```bash
leolong@TH:examples(* main)$ cargo run --example test
   Compiling datafusion-examples v46.0.0 (/home/<USER>/code/datafusion/datafusion-examples)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 1m 14s
     Running `/home/<USER>/code/datafusion/target/debug/examples/test`
DataSourceExec: partitions=0, partition_sizes=[]
```

## 三、Datafusion 逻辑计划的改写和优化

在 `DataFusion` 中，查询优化是提高查询性能的关键环节。优化器通过对逻辑计划进行一系列的改写和转换，以生成执行效率更高的等价计划。这一过程不会改变查询的语义，但可以显著提升查询执行的速度和资源利用效率。

### 3.1 优化器架构

DataFusion 的优化器架构**基于规则驱动(Rule-Based Optimizer, RBO)**的方式，通过应用一系列的优化规则（`OptimizerRule`）来重写逻辑计划。从代码实现来看，优化器主要包含以下核心组件：

1. **优化器规则接口** (`OptimizerRule` trait)：
```rust
pub trait OptimizerRule: Debug {
    // 规则名称
    fn name(&self) -> &str;
    
    // 规则应用的顺序（自上而下或自下而上）
    fn apply_order(&self) -> Option<ApplyOrder>;
    
    // 是否支持重写优化
    fn supports_rewrite(&self) -> bool;
    
    // 尝试重写计划
    fn rewrite(
        &self,
        plan: LogicalPlan,
        config: &dyn OptimizerConfig,
    ) -> Result<Transformed<LogicalPlan>>;
}
```

2. **优化器实现** (`Optimizer` 结构体)：
```rust
#[derive(Clone, Debug)]
pub struct Optimizer {
    // 所有要应用的优化器规则
    pub rules: Vec<Arc<dyn OptimizerRule + Send + Sync>>,
}
```

3. **内置优化规则**：DataFusion 默认包含了一系列优化规则：
```rust
impl Optimizer {
    pub fn new() -> Self {
        let rules: Vec<Arc<dyn OptimizerRule + Sync + Send>> = vec![
            Arc::new(EliminateNestedUnion::new()),          // 消除嵌套的Union
            Arc::new(SimplifyExpressions::new()),           // 简化表达式
            Arc::new(UnwrapCastInComparison::new()),        // 解包裹Cast操作
            Arc::new(ReplaceDistinctWithAggregate::new()),  // 将Distinct操作替换为Aggregate
            Arc::new(EliminateJoin::new()),                 // 消除Join操作
            Arc::new(DecorrelatePredicateSubquery::new()),  // 解关联子查询
            Arc::new(ScalarSubqueryToJoin::new()),          // 将标量子查询转换为Join
            Arc::new(ExtractEquijoinPredicate::new()),      // 提取等值连接条件
            Arc::new(EliminateDuplicatedExpr::new()),       // 消除重复表达式
            Arc::new(EliminateFilter::new()),               // 消除Filter操作
            Arc::new(EliminateCrossJoin::new()),            // 消除交叉连接
            Arc::new(CommonSubexprEliminate::new()),        // 公共子表达式消除
            Arc::new(EliminateLimit::new()),                // 消除Limit操作
            Arc::new(PropagateEmptyRelation::new()),        // 传播空关系
            Arc::new(EliminateOneUnion::new()),             // 消除单个Union
            Arc::new(FilterNullJoinKeys::default()),        // 过滤空连接键
            Arc::new(EliminateOuterJoin::new()),            // 消除外连接
            Arc::new(PushDownLimit::new()),                 // 下推Limit操作
            Arc::new(PushDownFilter::new()),                // 下推Filter操作
            Arc::new(SingleDistinctToGroupBy::new()),       // 将Distinct操作转换为GroupBy
            Arc::new(CommonSubexprEliminate::new()),        // 公共子表达式消除
            Arc::new(EliminateGroupByConstant::new()),      // 消除GroupBy常量
            Arc::new(OptimizeProjections::new()),           // 优化投影操作
            Arc::new(EliminateOneUnion::new()),             // 消除单个Union
            Arc::new(FilterNullJoinKeys::default()),        // 过滤空连接键
            Arc::new(EliminateOuterJoin::new()),            // 消除外连接
            Arc::new(PushDownLimit::new()),                 // 下推Limit操作
            Arc::new(PushDownFilter::new()),                // 下推Filter操作
            Arc::new(SingleDistinctToGroupBy::new()),       // 将Distinct操作转换为GroupBy
            Arc::new(CommonSubexprEliminate::new()),        // 公共子表达式消除
            Arc::new(EliminateGroupByConstant::new()),      // 消除GroupBy常量
            Arc::new(OptimizeProjections::new()),           // 优化投影操作
        ];
        Self::with_rules(rules)
    }
}
```

4. **优化过程**：优化器按照规则列表顺序依次应用每条规则，直到计划不再发生变化或达到最大迭代次数：
```rust
// 优化流程（简化版）
fn optimize(plan: LogicalPlan, config: &dyn OptimizerConfig) -> Result<LogicalPlan> {
    let mut current_plan = plan;
    let mut changed = true;
    let max_passes = config.max_passes();
    
    // 迭代应用规则，直到计划不再变化或达到最大迭代次数
    for _ in 0..max_passes {
        if !changed {
            break;
        }
        changed = false;
        
        // 应用每条优化规则
        for rule in &self.rules {
            if let Transformed::yes(new_plan) = rule.rewrite(current_plan, config)? {
                current_plan = new_plan;
                changed = true;
                break;  // 重新从第一条规则开始
            }
        }
    }
    
    Ok(current_plan)
}
```

除了优化器默认配置的标准优化规则，用户也可以通过 `Optimizer::with_rules()` 方法提供自定义的规则集：

```rust
// 使用自定义规则创建优化器
let rules: Vec<Arc<dyn OptimizerRule + Send + Sync>> = vec![
    Arc::new(PushDownFilter::new()),
    Arc::new(SimplifyExpressions::new()),
    // 其他自定义规则...
];

let optimizer = Optimizer::with_rules(rules);
```

### 3.2 优化规则接口

所有的优化规则都必须实现 `OptimizerRule` 特型（trait）：

```rust
pub trait OptimizerRule: Debug {
    // 规则名称
    fn name(&self) -> &str;
    
    // 规则应用的顺序（自上而下或自下而上）
    fn apply_order(&self) -> Option<ApplyOrder> {
        None  // 默认实现自己处理递归
    }
    
    // 尝试重写计划，返回 Transformed::yes 表示计划已改变
    fn rewrite(
        &self,
        plan: LogicalPlan,
        config: &dyn OptimizerConfig,
    ) -> Result<Transformed<LogicalPlan>>;
}
```

规则的应用顺序有两种主要模式：
- `ApplyOrder::TopDown`：先处理父节点，再处理子节点
- `ApplyOrder::BottomUp`：先处理子节点，再处理父节点

在实现自定义规则时，需要选择适合该规则特性的应用顺序。

### 3.3 核心优化规则

DataFusion 提供了丰富的内置优化规则，下面介绍一些最重要的规则及其工作原理。

#### 3.3.1 谓词下推 (PushDownFilter)

谓词下推是最常见也是最有效的优化技术之一，它的核心思想是将过滤条件尽可能早地应用，以减少需要处理的数据量。

**原理**：将 `Filter` 操作尽可能向下移动到靠近数据源的位置，甚至推入到数据源内部。

**示例**：
```
# 优化前：从表中读取所有数据，然后进行过滤
Filter: salary > 50000
  Sort: name ASC
    TableScan: employees

# 优化后：将过滤条件推入到数据源内部，先过滤数据，再排序
Sort: name ASC  
  Filter: salary > 50000  
    TableScan: employees
```

**注意事项**：并非所有过滤条件都可以下推。例如，针对聚合函数结果的过滤就不能下推到聚合之前：

```
# 不能下推
Filter: AVG(salary) > 50000
  Aggregate: GROUP BY department, AVG(salary)
    TableScan: employees
```

在更复杂的情况下，过滤表达式可能被拆分，一部分下推，一部分保留：

```
# 优化前：从表中读取所有数据，然后进行过滤
Filter: department = 'IT' AND AVG(salary) > 50000
  Aggregate: GROUP BY department, AVG(salary)
    TableScan: employees

# 优化后：将过滤条件 department = 'IT' 推入到数据源内部，先过滤数据，再聚合
Filter: AVG(salary) > 50000
  Aggregate: GROUP BY department, AVG(salary)
    Filter: department = 'IT'
      TableScan: employees
```

#### 3.3.2 投影下推 (ProjectionPushDown)

投影下推优化通过尽早移除不需要的列，减少数据处理和传输量。

**原理**：分析整个查询，确定哪些列是实际需要的，然后尽早地只选择这些列。

**示例**：
```
# 优化前：从表中读取所有数据(包括不需要的 email, department)，然后进行过滤
Projection: first_name, last_name
  Filter: salary > 50000
    TableScan: employees [id, first_name, last_name, email, salary, department]

# 优化后：去除 email, department 列，只读取需要的列
Projection: first_name, last_name
  Filter: salary > 50000
    TableScan: employees [first_name, last_name, salary]  // 只读取需要的列
```

这种优化对于**列式存储（如 Parquet）**尤其有效，因为它可以避免读取不需要的列数据。

#### 3.3.3 表达式简化 (SimplifyExpressions)

表达式简化优化通过计算常量表达式和应用代数简化规则，减少运行时计算量。

**原理**：在优化阶段计算常量表达式，应用布尔代数和算术简化规则。

**示例**：
```
# 优化前
Filter: price * 0 < 10
  TableScan: products

# 优化后
Filter: 0 < 10  // 可以进一步优化为 Filter: true
  TableScan: products

# 最终可能变为
TableScan: products  // 完全移除无意义的过滤
```

常见的表达式简化规则包括：
- 常量折叠：`1 + 2` → `3`
- 布尔代数简化：`a AND a` → `a`
- 冗余表达式消除：`a > 5 AND a > 3` → `a > 5`
- 恒等式处理：`x = x` → `true`
- 零值优化：`a * 0` → `0`

#### 3.3.4 限制下推 (PushDownLimit)

限制下推优化通过尽早应用 LIMIT 子句，减少后续操作需要处理的数据量。

**原理**：将 LIMIT 操作尽可能下推到查询计划的底部。

**示例**：
```
# 优化前
Limit: 10
  Projection: id, name
    TableScan: employees

# 优化后
Projection: id, name
  Limit: 10  // 可以安全下推到Projection之前
    TableScan: employees
```

需要注意的是，**LIMIT 不能下推到会改变结果顺序的操作之前**，例如 Sort、Aggregate 等。

#### 3.3.5 公共子表达式消除 (CommonSubexprEliminate)

公共子表达式消除（CSE）优化识别并复用查询中重复计算的表达式，减少冗余计算。

**原理**：找出查询中多次出现的相同表达式，计算一次并复用结果。

**示例**：
```
# 优化前：两次计算相同的表达式(price * quantity)
Projection: 
  (price * quantity) as total,
  (price * quantity) * tax_rate as tax_amount

# 优化后：复用计算结果，减少冗余计算
Projection:
  __common_expr_1 as total,
  __common_expr_1 * tax_rate as tax_amount
  SubQuery:
    Projection: (price * quantity) as __common_expr_1
```

CSE 特别适用于有复杂计算的查询，可以显著减少 CPU 使用和改善查询性能。

#### 3.3.6 交叉连接消除 (EliminateCrossJoin)

交叉连接消除优化将交叉连接（笛卡尔积）转换为等值连接，以提高连接性能。

**原理**：分析 WHERE 子句中的连接条件，将没有显式连接条件的 CROSS JOIN 转换为带条件的 INNER JOIN。

**示例**：
```
# SQL 查询
SELECT * FROM orders, customers WHERE orders.customer_id = customers.id;

# 优化前：交叉连接，返回笛卡尔积
Filter: orders.customer_id = customers.id
  CrossJoin:
    TableScan: orders
    TableScan: customers

# 优化后：内连接，只返回两表中都匹配的行
InnerJoin: orders.customer_id = customers.id
  TableScan: orders
  TableScan: customers
```

这种优化可以显著提高连接性能，因为等值连接可以使用哈希连接或排序合并连接等高效算法，而不是笛卡尔积的嵌套循环连接。

### 3.4 查看优化过程

DataFusion 提供了 `EXPLAIN VERBOSE` 命令，可以查看每条优化规则应用后的计划变化，这对于理解和调试查询优化非常有用。

**示例**：
```sql
EXPLAIN VERBOSE SELECT CAST(1 + 2.2 AS STRING) AS foo;
```

输出结果会显示每条规则应用后的计划状态：
```
+------------------------------------------------------------+---------------------------------------------------------------------------+
| plan_type                                                  | plan                                                                      |
+------------------------------------------------------------+---------------------------------------------------------------------------+
| initial_logical_plan                                       | Projection: CAST(Int64(1) + Float64(2.2) AS Utf8) AS foo                  |
|                                                            |   EmptyRelation                                                           |
| logical_plan after type_coercion                           | Projection: CAST(CAST(Int64(1) AS Float64) + Float64(2.2) AS Utf8) AS foo |
|                                                            |   EmptyRelation                                                           |
| logical_plan after simplify_expressions                    | Projection: Utf8("3.2") AS foo                                            |
|                                                            |   EmptyRelation                                                           |
| logical_plan after unwrap_cast_in_comparison               | SAME TEXT AS ABOVE                                                        |
...
| logical_plan                                               | Projection: Utf8("3.2") AS foo                                            |
|                                                            |   EmptyRelation                                                           |
+------------------------------------------------------------+---------------------------------------------------------------------------+
```

在这个例子中，我们可以清楚地看到优化过程：
1. `type_coercion` 规则将 Int64(1) 转换为 Float64 以便进行加法运算
2. `simplify_expressions` 规则在编译时计算了 `1 + 2.2` 的结果并转换为字符串
3. 后续规则未对计划做进一步修改

### 3.5 自定义优化规则

DataFusion 允许用户通过实现 `OptimizerRule` trait 来创建自定义优化规则。下面通过一个完整的示例来说明如何创建和使用自定义优化规则。

#### 3.5.1 示例场景

我们将实现一个优化规则，该规则会将所有形如 `column = constant` 的等值比较替换为自定义的 UDF 函数调用。具体来说：
- 将 `age = 22` 转换为 `my_eq(age, 22)`
- 这个 UDF 在本例中总是返回 `true`（仅用于演示）

#### 3.5.2 实现优化规则

首先，定义优化规则结构体：

```rust
#[derive(Default, Debug)]
struct MyOptimizerRule {}

impl OptimizerRule for MyOptimizerRule {
    // 规则名称
    fn name(&self) -> &str {
        "my_optimizer_rule"
    }

    // 使用更高效的 rewrite API
    fn supports_rewrite(&self) -> bool {
        true
    }

    // 采用自底向上的遍历方式
    fn apply_order(&self) -> Option<ApplyOrder> {
        Some(ApplyOrder::BottomUp)
    }

    // 重写逻辑计划
    fn rewrite(
        &self,
        plan: LogicalPlan,
        _config: &dyn OptimizerConfig,
    ) -> Result<Transformed<LogicalPlan>> {
        plan.map_expressions(|expr| self.rewrite_expr(expr))
    }
}
```

实现表达式重写逻辑：

```rust
impl MyOptimizerRule {
    fn rewrite_expr(&self, expr: Expr) -> Result<Transformed<Expr>> {
        // 对表达式树进行自底向上的遍历
        expr.transform_up(|expr| {
            match expr {
                // 匹配二元等值表达式
                Expr::BinaryExpr(binary_expr) if is_binary_eq(&binary_expr) => {
                    let BinaryExpr { left, op: _, right } = binary_expr;
                    // 创建 UDF 调用替换原始表达式
                    let udf = ScalarUDF::new_from_impl(MyEq::new());
                    let call = udf.call(vec![*left, *right]);
                    Ok(Transformed::yes(call))
                }
                // 其他表达式保持不变
                _ => Ok(Transformed::no(expr)),
            }
        })
    }
}

// 辅助函数：检查是否为等值表达式
fn is_binary_eq(binary_expr: &BinaryExpr) -> bool {
    binary_expr.op == Operator::Eq
        && is_lit_or_col(binary_expr.left.as_ref())
        && is_lit_or_col(binary_expr.right.as_ref())
}

// 辅助函数：检查是否为列引用或字面量
fn is_lit_or_col(expr: &Expr) -> bool {
    matches!(expr, Expr::Column(_) | Expr::Literal(_))
}
```

#### 3.5.3 实现自定义 UDF

为了完成优化规则，我们需要实现将替换原始表达式的 UDF：

```rust
#[derive(Debug, Clone)]
struct MyEq {
    signature: Signature,
}

impl MyEq {
    fn new() -> Self {
        Self {
            signature: Signature::any(2, Volatility::Stable)
        }
    }
}

impl ScalarUDFImpl for MyEq {
    fn name(&self) -> &str {
        "my_eq"
    }

    fn signature(&self) -> &Signature {
        &self.signature
    }

    fn return_type(&self, _arg_types: &[DataType]) -> Result<DataType> {
        Ok(DataType::Boolean)
    }

    // 示例实现：始终返回 true
    fn invoke_with_args(&self, _args: ScalarFunctionArgs) -> Result<ColumnarValue> {
        Ok(ColumnarValue::Scalar(ScalarValue::from(true)))
    }
}
```

#### 3.5.4 使用优化规则

将自定义优化规则添加到 DataFusion 会话中：

```rust
let ctx = SessionContext::new();
ctx.add_optimizer_rule(Arc::new(MyOptimizerRule {}));
```

#### 3.5.5 优化效果

对于查询 `SELECT * FROM person WHERE age = 22`：

1. **优化前的逻辑计划**：
```
Filter: person.age = Int32(22)
  TableScan: person projection=[name, age]
```

2. **优化后的逻辑计划**：
```
Filter: my_eq(person.age, Int32(22))
  TableScan: person projection=[name, age]
```

3. **查询结果**：由于示例中的 `my_eq` 总是返回 `true`，所以会返回表中的所有记录。

需要注意的是，这个优化规则只会触发于等值比较（`=`），对于其他比较操作符（如 `<>`）不会进行转换。这可以通过 `is_binary_eq` 函数中的条件判断来控制。

#### 3.5.6 实践建议

在实现自定义优化规则时，需要注意以下几点：

1. **正确性**：确保转换后的表达式在语义上等价于原始表达式
2. **性能影响**：评估优化规则是否真的能提升查询性能
3. **应用范围**：明确定义规则的适用条件，避免过度优化
4. **调试支持**：添加适当的日志或调试信息，便于追踪优化过程
5. **错误处理**：妥善处理转换过程中可能出现的异常情况
通过这个示例，我们可以看到 DataFusion 提供了灵活的扩展机制，允许用户根据具体需求实现自定义的优化规则。这种能力对于特定场景下的性能优化非常有用。

