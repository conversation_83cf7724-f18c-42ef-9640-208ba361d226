digraph UnifiedSemanticQueryEngine {
    // 图形属性设置
    rankdir=TB;
    node [shape=box, style=filled, fontname="SimHei", fontsize=10];
    edge [fontname="SimHei", fontsize=9];

    // 用户输入层
    subgraph cluster_input {
        label="多模态查询输入";
        style=filled;
        color=lightgray;

        structured_query [label="结构化查询\n扩展SQL语法\n(IMAGE_SIMILAR_TO,\nTEXT_CONTAINS,\nAUDIO_MATCHES)", fillcolor=lightblue];
        api_interface [label="程序化API接口\n现代化API设计\n多模态内容原生支持", fillcolor=lightcyan];
        natural_language [label="自然语言查询\n意图识别模型\n跨模态语义对齐", fillcolor=lightgreen];
    }

    // 查询解析与语义理解模块
    query_parsing [label="查询解析与语义理解模块\n基于USR模型\n统一处理多种输入", fillcolor=yellow];

    // USR模型核心组件
    subgraph cluster_usr_model {
        label="USR模型 (统一语义表示模型)";
        style=filled;
        color=lightyellow;

        // SEncoder编码器
        subgraph cluster_sencoder {
            label="SEncoder编码器";
            style=filled;
            color=white;

            text_encoder [label="文本SEncoder\nE_text(x_text)\nTransformer架构\n768维语义向量", fillcolor=lightpink];
            image_encoder [label="图像SEncoder\nE_image(x_image)\nResNet/ViT架构\n跨模态对齐", fillcolor=lightpink];
            audio_encoder [label="音频SEncoder\nE_audio(x_audio)\nWav2Vec架构", fillcolor=lightpink];
            video_encoder [label="视频SEncoder\nE_video(x_video)\n时空卷积网络", fillcolor=lightpink];
        }

        unified_semantic_vector [label="统一语义向量\n768维L2归一化\n向量模长=1\n维度值域[-1,1]", fillcolor=lightcoral, shape=ellipse];
    }

    // MFusion融合器
    subgraph cluster_mfusion {
        label="MFusion融合器 (多模态融合器)";
        style=filled;
        color=lightsteelblue;

        // 三因子动态权重计算
        subgraph cluster_three_factors {
            label="三因子动态权重计算";
            style=filled;
            color=white;

            semantic_factor [label="语义相关性系数α_i\n余弦相似度计算\nα_i=max(0,cos(q,E_mi(xi)))\n值域[0,1]", fillcolor=lightgreen];
            execution_factor [label="执行计划感知系数γ_i\nPostgreSQL执行计划解析\n成本倒数×并行效率×索引利用\n对数变换标准化", fillcolor=lightgreen];
            resource_factor [label="资源效率系数δ_i\n实时资源监控\nδ_i=exp(-λ·R_i)\nCPU/内存/IO利用率", fillcolor=lightgreen];
        }

        weight_calculation [label="权重归一化计算\nβ_i = (α_i·γ_i·δ_i) / Σ(α_j·γ_j·δ_j)\n确保Σβ_i=1且β_i≥0", fillcolor=gold, shape=ellipse];

        fusion_formula [label="多模态融合公式\nF(E_m1(x1),...,E_mn(xn))\n= Σ(β_i·E_mi(xi))", fillcolor=lightcoral, shape=ellipse];
    }

    // 查询优化与规划模块
    subgraph cluster_query_optimization {
        label="查询优化与规划模块";
        style=filled;
        color=lavender;

        semantic_aware_analysis [label="语义感知查询分析\n查询语义特征识别\n模态类型分析\n语义复杂度评估", fillcolor=lightcyan];
        intelligent_index_selection [label="智能索引选择\n语义向量索引\nLSH/HNSW技术\n多模态索引策略", fillcolor=lightcyan];
        execution_plan_generation [label="语义感知执行计划生成\n基于MFusion权重结果\n动态优先级调整\n资源分配策略", fillcolor=lightcyan];
        postgresql_integration [label="PostgreSQL集成\n执行计划获取\nJSON格式解析\n特征标准化接口", fillcolor=orange];
    }

    // 多模态查询执行模块
    execution_module [label="多模态查询执行模块\n执行计划感知\n动态权重调整", fillcolor=lightgreen, shape=ellipse];

    // 并行执行路径
    subgraph cluster_execution_paths {
        label="并行执行路径";
        style=filled;
        color=mistyrose;

        text_retrieval_path [label="文本检索路径\n文本语义检索\n索引扫描优化", fillcolor=pink];
        image_retrieval_path [label="图像检索路径\n图像语义匹配\n高维向量处理", fillcolor=pink];
        cross_modal_path [label="跨模态关联路径\n跨模态语义对齐\n关联强度计算", fillcolor=pink];
    }

    // 动态权重调整机制
    subgraph cluster_dynamic_adjustment {
        label="动态权重调整机制";
        style=filled;
        color=honeydew;

        execution_monitoring [label="执行监控与反馈收集\n毫秒级数据采集\nCPU/内存/IO监控\n结果质量评估", fillcolor=lightgray];
        trigger_conditions [label="触发条件判断\n性能偏差>10%\n资源变化>15%\n质量下降<85%", fillcolor=lightgray];
        weight_recalculation [label="权重重新计算\n三因子重新评估\n指数平滑算法\nβ_i^new=α_smooth·β_i^calc+(1-α_smooth)·β_i^old", fillcolor=lightgray];
        convergence_check [label="收敛判断\n连续变化<阈值\n权重配置稳定", fillcolor=lightgray];
    }

    // 查询结果融合模块
    subgraph cluster_result_fusion {
        label="查询结果融合模块";
        style=filled;
        color=aliceblue;

        weighted_fusion_scoring [label="加权融合评分\nScore_final = Σ(β_i × Score_semantic_i)\n多路径结果整合", fillcolor=white];
        semantic_aware_sorting [label="语义感知结果排序\n基于USR模型\n统一语义空间相似度", fillcolor=white];
        cross_modal_alignment_cache [label="跨模态对齐缓存\n存储1000次查询结果\n加速相似查询处理", fillcolor=white];
    }

    // 自优化机制
    subgraph cluster_self_optimization {
        label="基于执行反馈的自优化机制";
        style=filled;
        color=lightsteelblue;

        feedback_collection [label="执行反馈数据收集\n性能指标统计\n用户满意度评估", fillcolor=lightblue];
        reward_function [label="强化学习奖励函数\nR = α·Quality + β·(1/ExecutionTime) + γ·(1/ResourceCost)", fillcolor=lightblue];
        gradient_descent [label="梯度下降参数更新\nAdam优化器\n学习率α=0.01\n超参数微调", fillcolor=lightblue];
        parameter_validation [label="参数验证与回滚\n交叉验证\n性能退化检测\n自动回滚机制", fillcolor=lightblue];
    }

    // 异常处理机制
    subgraph cluster_exception_handling {
        label="异常处理与降级机制";
        style=filled;
        color=mistyrose;

        exception_detection [label="异常检测\n执行计划缺失\n权重计算异常\n性能下降检测", fillcolor=pink];
        default_gamma [label="默认γ系数\n执行计划信息缺失时\n预设默认值", fillcolor=pink];
        backup_weights [label="历史统计备用配置\n权重计算异常时\n快速切换机制", fillcolor=pink];
        auto_correction [label="自动参数校正\n性能下降时\n自适应调整", fillcolor=pink];
    }

    // 数据存储层
    subgraph cluster_data_storage {
        label="多模态数据存储";
        style=filled;
        color=lavender;

        data_preprocessing [label="多模态数据预处理\n语义编码流程\n批量处理优化", fillcolor=lightcyan];
        semantic_vector_storage [label="语义向量存储\n扩展表结构\n一对一映射关系", fillcolor=lightcyan];
        vector_indexing [label="向量索引系统\nLSH/HNSW算法\n高维向量优化", fillcolor=lightcyan];
    }

    // 最终查询结果
    final_result [label="最终查询结果\n语义匹配准确\n执行效率优化", fillcolor=lightgreen, shape=ellipse];

    // 主要连接关系
    structured_query -> query_parsing;
    api_interface -> query_parsing;
    natural_language -> query_parsing;

    query_parsing -> text_encoder;
    query_parsing -> image_encoder;
    query_parsing -> audio_encoder;
    query_parsing -> video_encoder;

    text_encoder -> unified_semantic_vector;
    image_encoder -> unified_semantic_vector;
    audio_encoder -> unified_semantic_vector;
    video_encoder -> unified_semantic_vector;

    unified_semantic_vector -> semantic_factor;
    unified_semantic_vector -> execution_factor;
    unified_semantic_vector -> resource_factor;

    semantic_factor -> weight_calculation;
    execution_factor -> weight_calculation;
    resource_factor -> weight_calculation;

    weight_calculation -> fusion_formula;
    fusion_formula -> semantic_aware_analysis;

    semantic_aware_analysis -> intelligent_index_selection;
    intelligent_index_selection -> execution_plan_generation;
    execution_plan_generation -> postgresql_integration;
    postgresql_integration -> execution_module;

    execution_module -> text_retrieval_path;
    execution_module -> image_retrieval_path;
    execution_module -> cross_modal_path;

    text_retrieval_path -> execution_monitoring;
    image_retrieval_path -> execution_monitoring;
    cross_modal_path -> execution_monitoring;

    execution_monitoring -> trigger_conditions;
    trigger_conditions -> weight_recalculation [label="触发"];
    trigger_conditions -> weighted_fusion_scoring [label="维持"];

    weight_recalculation -> convergence_check;
    convergence_check -> weight_recalculation [label="未收敛", style=dashed];
    convergence_check -> weighted_fusion_scoring [label="收敛"];

    weighted_fusion_scoring -> semantic_aware_sorting;
    semantic_aware_sorting -> cross_modal_alignment_cache;
    cross_modal_alignment_cache -> final_result;

    final_result -> feedback_collection;
    feedback_collection -> reward_function;
    reward_function -> gradient_descent;
    gradient_descent -> parameter_validation;
    parameter_validation -> weight_calculation [label="权重更新", style=dashed, color=blue];

    // 异常处理连接
    execution_factor -> exception_detection [style=dashed, color=red];
    exception_detection -> default_gamma [label="执行计划缺失"];
    exception_detection -> backup_weights [label="权重计算异常"];
    exception_detection -> auto_correction [label="性能下降"];

    default_gamma -> weight_calculation [style=dashed, color=orange];
    backup_weights -> weight_calculation [style=dashed, color=orange];
    auto_correction -> weight_calculation [style=dashed, color=orange];

    // 数据存储连接
    data_preprocessing -> semantic_vector_storage;
    semantic_vector_storage -> vector_indexing;
    vector_indexing -> execution_module;

    // 关键技术创新标注
    subgraph cluster_innovations {
        label="四大技术创新点";
        style=filled;
        color=gold;

        innovation1 [label="USR模型\n统一语义表示\nSEncoder+MFusion", fillcolor=yellow];
        innovation2 [label="执行计划感知\n动态权重自适应\n三因子计算模型", fillcolor=yellow];
        innovation3 [label="语义感知优化\n跨模态查询支持\n智能索引选择", fillcolor=yellow];
        innovation4 [label="自优化机制\n强化学习驱动\n持续性能改进", fillcolor=yellow];
    }

    innovation1 -> unified_semantic_vector [style=dashed, color=gold];
    innovation2 -> weight_calculation [style=dashed, color=gold];
    innovation3 -> execution_plan_generation [style=dashed, color=gold];
    innovation4 -> gradient_descent [style=dashed, color=gold];
}