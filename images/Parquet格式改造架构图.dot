digraph ParquetFormatArchitecture {
    rankdir=TB;
    node [shape=box, style=filled, fontname="Arial"];
    edge [fontname="Arial"];
    
    // 顶层：改造后的Parquet格式
    enhanced_parquet [label="增强多模态Parquet格式", fillcolor=lightblue, fontsize=14, style="filled,bold"];
    
    // 两个核心技术组件
    subgraph cluster_core_components {
        label="核心技术组件";
        style=filled;
        color=lightblue;
        
        mafe [label="模态感知格式扩展\n(MAFE)", fillcolor=lightblue];
        moes [label="模态优化编码策略\n(MOES)", fillcolor=lightblue];
    }
    
    // MAFE的子组件
    subgraph cluster_mafe_components {
        label="模态感知格式扩展组件";
        style=filled;
        color=lightcyan;
        
        logical_types [label="逻辑类型系统扩展", fillcolor=lightgreen];
        metadata_enhancement [label="元数据结构增强", fillcolor=lightyellow];
    }
    
    // 支持的模态类型
    subgraph cluster_modal_types {
        label="支持的模态类型";
        style=filled;
        color=lightgreen;
        
        image_type [label="IMAGE类型", fillcolor=lightgreen];
        audio_type [label="AUDIO类型", fillcolor=lightgreen];
        video_type [label="VIDEO类型", fillcolor=lightgreen];
        vector_type [label="VECTOR类型", fillcolor=lightgreen];
    }
    
    // 元数据层次
    subgraph cluster_metadata_layers {
        label="四层元数据结构";
        style=filled;
        color=lightyellow;
        
        file_metadata [label="文件级元数据", fillcolor=lightyellow];
        rowgroup_metadata [label="行组级元数据", fillcolor=lightyellow];
        column_metadata [label="列级元数据", fillcolor=lightyellow];
        page_metadata [label="页级元数据", fillcolor=lightyellow];
    }
    
    // MOES的子组件
    subgraph cluster_moes_components {
        label="模态优化编码策略组件";
        style=filled;
        color=lightcoral;
        
        native_encoding [label="原生编码保留", fillcolor=lightcoral];
        smart_chunking [label="智能分块策略", fillcolor=lightcoral];
        vector_optimization [label="向量数据优化", fillcolor=lightcoral];
    }
    
    // 底层存储
    subgraph cluster_storage {
        label="底层存储层";
        style=filled;
        color=lightsteelblue;
        
        physical_storage [label="物理存储层", fillcolor=lightsteelblue];
    }
    
    // 主要连接关系
    enhanced_parquet -> mafe;
    enhanced_parquet -> moes;
    
    mafe -> logical_types;
    mafe -> metadata_enhancement;
    
    logical_types -> image_type;
    logical_types -> audio_type;
    logical_types -> video_type;
    logical_types -> vector_type;
    
    metadata_enhancement -> file_metadata;
    metadata_enhancement -> rowgroup_metadata;
    metadata_enhancement -> column_metadata;
    metadata_enhancement -> page_metadata;
    
    moes -> native_encoding;
    moes -> smart_chunking;
    moes -> vector_optimization;
    
    moes -> physical_storage;
    
    // 元数据层次关系
    file_metadata -> rowgroup_metadata [style=bold];
    rowgroup_metadata -> column_metadata [style=bold];
    column_metadata -> page_metadata [style=bold];
    
    // 跨组件协作关系
    logical_types -> native_encoding [style=dotted, color=blue, label="语义信息"];
    metadata_enhancement -> smart_chunking [style=dotted, color=blue, label="统计特征"];
}
