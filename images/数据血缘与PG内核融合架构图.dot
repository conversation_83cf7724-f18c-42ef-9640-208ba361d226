digraph PostgreSQL查询处理流程扩展 {
    // 基本设置
    rankdir=TB;
    compound=true;  // 允许连接到子图
    nodesep=0.5;    // 节点间距
    ranksep=0.8;    // 层级间距
    splines=ortho;  // 使用正交线条减少交叉
    
    // 节点样式定义
    node [shape=box, style=filled, fontname="Arial", fontsize=11];
    edge [fontname="Arial", fontsize=10];
    
    // 定义节点
    SQL [label="SQL查询输入", shape=parallelogram, fillcolor=lightblue];
    Result [label="查询结果", shape=parallelogram, fillcolor=lightblue];
    
    // 处理阶段子图 - 将所有处理阶段放入一个子图中
    subgraph cluster_processing {
        label="PostgreSQL查询处理流程";
        style="rounded";
        color=gray;
        fontcolor=black;
        bgcolor=aliceblue;
        
        // 处理阶段节点
        Parser [label="解析阶段\n(Parser)", fillcolor=lightcoral];
        Analyzer [label="分析阶段\n(Analyzer)", fillcolor=lightcoral];
        Rewriter [label="重写阶段\n(Rewriter)", fillcolor=lightcoral];
        Planner [label="规划阶段\n(Planner)", fillcolor=lightcoral];
        Executor [label="执行阶段\n(Executor)", fillcolor=lightcoral];
        
        // 处理阶段内部流程连接
        Parser -> Analyzer -> Rewriter -> Planner -> Executor [weight=10];
        
        // 强制水平排列
        {rank=same; Parser; Analyzer; Rewriter; Planner; Executor;}
    }
    
    // 血缘采集部分
    subgraph cluster_lineage_collection {
        label="数据血缘采集与存储";
        style="rounded";
        color=darkgreen;
        fontcolor=darkgreen;
        bgcolor=honeydew;
        
        LineageCollector [label="数据血缘信息收集器\n(LineageCollector)", fillcolor=lightgreen];
        Storage [label="数据血缘存储\n(pg_lineage schema)", fillcolor=lightgreen];
        Tables [
            label=<<TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0" CELLPADDING="4">
                <TR><TD COLSPAN="1" BGCOLOR="#2E8B57"><FONT COLOR="white" POINT-SIZE="12"><B>血缘相关表</B></FONT></TD></TR>
                <TR><TD BGCOLOR="#E6FFE6">query_record</TD></TR>
                <TR><TD BGCOLOR="#D1EEDB">table_lineage</TD></TR>
                <TR><TD BGCOLOR="#E6FFE6">column_lineage</TD></TR>
                <TR><TD BGCOLOR="#D1EEDB">execution_plan</TD></TR>
                <TR><TD BGCOLOR="#E6FFE6">lineage_graph</TD></TR>
                <TR><TD BGCOLOR="#D1EEDB">lineage_statistics</TD></TR>
            </TABLE>>, 
            shape=none, 
            margin=0
        ];
        
        // 内部连接
        LineageCollector -> Storage -> Tables;
    }
    
    // 优化反馈子图
    subgraph cluster_optimization {
        label="优化反馈机制";
        style="rounded";
        color=darkblue;
        fontcolor=darkblue;
        bgcolor=lavender;
        
        // 节点定义
        MathModels [
            label=<<TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0" CELLPADDING="4">
                <TR><TD COLSPAN="1" BGCOLOR="#4682B4"><FONT COLOR="white" POINT-SIZE="12"><B>数据血缘数学模型</B></FONT></TD></TR>
                <TR><TD BGCOLOR="#E6F3FF">血缘复杂度量化模型</TD></TR>
                <TR><TD BGCOLOR="#D6EBFF">血缘关系权重模型</TD></TR>
                <TR><TD BGCOLOR="#E6F3FF">血缘感知查询优化模型</TD></TR>
            </TABLE>>, 
            shape=none, 
            margin=0
        ];
        Performance [
            label=<<TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0" CELLPADDING="4">
                <TR><TD COLSPAN="1" BGCOLOR="#4682B4"><FONT COLOR="white" POINT-SIZE="12"><B>性能优化策略</B></FONT></TD></TR>
                <TR><TD BGCOLOR="#E6F3FF">异步持久化</TD></TR>
                <TR><TD BGCOLOR="#D6EBFF">配置采集粒度</TD></TR>
                <TR><TD BGCOLOR="#E6F3FF">采样策略</TD></TR>
            </TABLE>>, 
            shape=none, 
            margin=0
        ];
        Adaptive [label="自适应优化框架", fillcolor=lightblue];
    }
    
    // 应用层子图
    subgraph cluster_application {
        label="应用层";
        style="rounded";
        color=darkred;
        fontcolor=darkred;
        bgcolor=mistyrose;
        
        API [label="数据血缘查询与分析API", fillcolor=lightpink];
        Apps [
            label=<<TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0" CELLPADDING="4">
                <TR><TD COLSPAN="1" BGCOLOR="#CD5C5C"><FONT COLOR="white" POINT-SIZE="12"><B>应用层</B></FONT></TD></TR>
                <TR><TD BGCOLOR="#FFE6E6">数据影响分析</TD></TR>
                <TR><TD BGCOLOR="#FFD6D6">异常数据溯源</TD></TR>
                <TR><TD BGCOLOR="#FFE6E6">合规与审计</TD></TR>
                <TR><TD BGCOLOR="#FFD6D6">血缘可视化</TD></TR>
                <TR><TD BGCOLOR="#FFE6E6">查询优化建议</TD></TR>
            </TABLE>>, 
            shape=none, 
            margin=0
        ];
        
        // 内部连接
        API -> Apps;
    }
    
    // 主流程连接
    SQL -> Parser [lhead=cluster_processing];
    Executor -> Result;
    
    // 从处理阶段到血缘收集器的连接 - 使用集中的连接点减少线条混乱
    edge [color="black"];
    Parser   -> LineageCollector [xlabel="on_raw_parse_extract_basic_lineage_callback", fontsize=9, fontcolor=gray, minlen=2];
    Analyzer -> LineageCollector [xlabel="on_analyze_identify_relations_callback", fontsize=9, fontcolor=gray, minlen=2];
    Rewriter -> LineageCollector [xlabel="on_rewrite_track_changes_callback", fontsize=9, fontcolor=gray, minlen=2];
    Planner  -> LineageCollector [xlabel="1.血缘感知查询优化\n2.PlanLineageExtractor", fontsize=9, fontcolor=gray, minlen=2];
    Executor -> LineageCollector [xlabel="1.on_executor_start_initialize_tracker_callback\n2.节点处理函数的血缘采集点\n3.on_executor_end_finalize_lineage_callback", fontsize=9, fontcolor=gray, minlen=2];
    
    // 优化反馈连接 - 使用不同颜色和样式区分，全部改用xlabel
    edge [style=dashed, fontsize=9];
    Adaptive -> Planner [color="blue", xlabel="自适应优化反馈\n(基于历史执行数据动态调整查询优化器参数)"];
    MathModels -> LineageCollector [color="purple", xlabel="数学模型指导\n(模型指导血缘采集流程)"];
    MathModels -> Planner [color="purple", xlabel="优化决策支持\n(优化计划选择)"];
    Performance -> LineageCollector [color="orange", xlabel="优化采集性能"];
    Performance -> Storage [color="orange", xlabel="优化存储效率"];
    
    // 数据流向连接
    edge [style=solid];
    Tables -> API;
    
    // 血缘相关表到优化反馈使用虚线
    edge [style=dashed];
    Tables -> Adaptive [color=blue, xlabel="血缘数据\n反馈优化"];
    
    // 强制布局调整
    {rank=same; LineageCollector; SQL; Result;}  // 确保主要节点在同一水平线
    {rank=same; Storage; MathModels;}            // 确保存储和模型在同一水平线
    {rank=same; Tables; Performance;}            // 确保表和性能优化在同一水平线
}
