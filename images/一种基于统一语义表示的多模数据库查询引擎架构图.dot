digraph QueryEngineArchitecture {
    // 图形属性设置
    rankdir=TB;
    node [shape=box, style=filled, fontname="Arial"];
    edge [fontname="Arial"];
    
    // 定义颜色主题
    subgraph cluster_usr_model {
        label="USR模型 (Unified Semantic Representation Model)";
        style=filled;
        fillcolor=lightblue;
        fontsize=14;
        fontweight=bold;
        
        SEncoder [label="SEncoder编码器\n(Semantic Encoder)", fillcolor=lightgreen];
        MFusion [label="MFusion融合器\n(Multi-modal Fusion)", fillcolor=lightgreen];
        SemanticSpace [label="统一语义空间\n(d维向量空间)", fillcolor=lightyellow];
        
        SEncoder -> SemanticSpace [label="编码映射"];
        MFusion -> SemanticSpace [label="融合输出"];
    }
    
    // 输入数据源
    subgraph cluster_input {
        label="多模态数据输入";
        style=filled;
        fillcolor=lightgray;
        
        TextData [label="文本数据", fillcolor=white];
        ImageData [label="图像数据", fillcolor=white];
        AudioData [label="音频数据", fillcolor=white];
        VideoData [label="视频数据", fillcolor=white];
    }
    
    // 查询输入
    subgraph cluster_query_input {
        label="查询输入方式";
        style=filled;
        fillcolor=lightcyan;
        
        SQLQuery [label="扩展SQL查询", fillcolor=white];
        APIQuery [label="程序化API", fillcolor=white];
        NLQuery [label="自然语言查询", fillcolor=white];
    }
    
    // 五个核心模块
    subgraph cluster_modules {
        label="查询引擎核心模块";
        style=filled;
        fillcolor=lightyellow;
        
        Module1 [label="统一语义表示与\n多模态语义空间模块", fillcolor=lightgreen];
        Module2 [label="查询解析与\n语义理解模块", fillcolor=lightcoral];
        Module3 [label="查询优化与\n规划模块", fillcolor=lightpink];
        Module4 [label="多模态查询\n执行模块", fillcolor=lightsteelblue];
        Module5 [label="查询结果\n融合模块", fillcolor=lightsalmon];
    }
    
    // 输出结果
    ResultOutput [label="高质量查询结果", fillcolor=lightgreen, shape=ellipse];
    
    // 数据流连接
    TextData -> SEncoder [label="文本编码"];
    ImageData -> SEncoder [label="图像编码"];
    AudioData -> SEncoder [label="音频编码"];
    VideoData -> SEncoder [label="视频编码"];
    
    SQLQuery -> Module2 [label="查询解析"];
    APIQuery -> Module2 [label="接口调用"];
    NLQuery -> Module2 [label="语言理解"];
    
    SEncoder -> Module1 [label="语义编码"];
    MFusion -> Module1 [label="模态融合"];
    
    Module1 -> Module2 [label="语义表示"];
    Module2 -> Module3 [label="查询向量"];
    Module3 -> Module4 [label="执行计划"];
    Module4 -> Module5 [label="查询结果"];
    Module5 -> ResultOutput [label="融合排序"];
    
    // USR模型与模块的关联
    SemanticSpace -> Module2 [label="统一空间", style=dashed];
    SemanticSpace -> Module3 [label="语义特征", style=dashed];
    SemanticSpace -> Module4 [label="向量操作", style=dashed];
    SemanticSpace -> Module5 [label="语义融合", style=dashed];
    
    // 创新点标注
    Innovation1 [label="创新点1:\nUSR模型", shape=diamond, fillcolor=gold];
    Innovation2 [label="创新点2:\n语义感知优化", shape=diamond, fillcolor=gold];
    Innovation3 [label="创新点3:\n多模态执行融合", shape=diamond, fillcolor=gold];
    
    Innovation1 -> Module1 [style=dotted, color=red];
    Innovation2 -> Module3 [style=dotted, color=red];
    Innovation3 -> Module4 [style=dotted, color=red];
    Innovation3 -> Module5 [style=dotted, color=red];
    
    // 跨模态查询示例
    subgraph cluster_examples {
        label="跨模态查询示例";
        style=filled;
        fillcolor=mistyrose;
        
        Example1 [label="以图搜文", fillcolor=white];
        Example2 [label="以文搜图", fillcolor=white];
        Example3 [label="多模态混合查询", fillcolor=white];
    }
    
    Example1 -> Module2 [style=dashed, color=blue];
    Example2 -> Module2 [style=dashed, color=blue];
    Example3 -> Module2 [style=dashed, color=blue];
}
